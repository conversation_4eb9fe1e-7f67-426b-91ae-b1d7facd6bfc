using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OrderTracking.API.Services;
using System;
using System.Collections.Generic;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    /// Health check controller with logging
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [AllowAnonymous]
    public class HealthController : ControllerBase
    {
        private readonly ILogger<HealthController> _logger;
        private readonly IApplicationLoggingService _appLogger;

        public HealthController(ILogger<HealthController> logger, IApplicationLoggingService appLogger)
        {
            _logger = logger;
            _appLogger = appLogger;
        }

        /// <summary>
        /// Basic health check endpoint
        /// </summary>
        /// <returns>Health status</returns>
        [HttpGet]
        public IActionResult Get()
        {
            try
            {
                var healthStatus = new
                {
                    status = "Healthy",
                    timestamp = DateTime.UtcNow,
                    version = "1.0.0",
                    environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
                };

                _appLogger.LogInformation("Health check requested", new { 
                    RequestSource = HttpContext.Connection.RemoteIpAddress?.ToString(),
                    UserAgent = Request.Headers["User-Agent"].ToString()
                });

                return Ok(healthStatus);
            }
            catch (Exception ex)
            {
                _appLogger.LogError(ex, "Health check failed");
                return StatusCode(500, new { status = "Unhealthy", error = "Internal server error" });
            }
        }

        /// <summary>
        /// Detailed health check with dependency status
        /// </summary>
        /// <returns>Detailed health status</returns>
        [HttpGet("detailed")]
        public IActionResult GetDetailed()
        {
            try
            {
                var checks = new List<object>();
                
                // Add your dependency checks here
                checks.Add(new { service = "Application Insights", status = "Available" });
                checks.Add(new { service = "Authentication", status = "Available" });

                var healthStatus = new
                {
                    status = "Healthy",
                    timestamp = DateTime.UtcNow,
                    version = "1.0.0",
                    environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                    checks = checks
                };

                _appLogger.TrackEvent("DetailedHealthCheck", new Dictionary<string, string>
                {
                    { "RequestSource", HttpContext.Connection.RemoteIpAddress?.ToString() },
                    { "UserAgent", Request.Headers["User-Agent"].ToString() },
                    { "CheckCount", checks.Count.ToString() }
                });

                return Ok(healthStatus);
            }
            catch (Exception ex)
            {
                _appLogger.LogError(ex, "Detailed health check failed");
                return StatusCode(500, new { status = "Unhealthy", error = "Internal server error" });
            }
        }
    }
}
