using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;

namespace OrderTracking.API.Middleware
{
    /// <summary>
    /// Global exception handling middleware that logs errors using Microsoft.Extensions.Logging
    /// </summary>
    public class GlobalExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionMiddleware> _logger;
        private readonly TelemetryClient _telemetryClient;

        public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger, TelemetryClient telemetryClient)
        {
            _next = next;
            _logger = logger;
            _telemetryClient = telemetryClient;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(context, ex);
            }
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            var userId = context.User?.Identity?.Name ?? "Anonymous";
            var requestPath = context.Request.Path.Value;
            var requestMethod = context.Request.Method;
            var userAgent = context.Request.Headers["User-Agent"].ToString();
            var remoteIp = context.Connection.RemoteIpAddress?.ToString();
            var traceId = context.TraceIdentifier;

            // Use structured logging with Microsoft.Extensions.Logging
            using var scope = _logger.BeginScope(new Dictionary<string, object>
            {
                ["RequestPath"] = requestPath,
                ["RequestMethod"] = requestMethod,
                ["UserId"] = userId,
                ["UserAgent"] = userAgent,
                ["RemoteIP"] = remoteIp,
                ["TraceId"] = traceId
            });

            _logger.LogError(exception,
                "Unhandled exception occurred for {UserId} accessing {RequestMethod} {RequestPath} from {RemoteIP}. TraceId: {TraceId}",
                userId, requestMethod, requestPath, remoteIp, traceId);

            // Log to Application Insights with structured properties
            var properties = new Dictionary<string, string>
            {
                { "RequestPath", requestPath ?? "" },
                { "RequestMethod", requestMethod },
                { "UserId", userId },
                { "UserAgent", userAgent },
                { "RemoteIP", remoteIp ?? "" },
                { "TraceId", traceId },
                { "ExceptionType", exception.GetType().Name },
                { "ExceptionMessage", exception.Message }
            };

            _telemetryClient.TrackException(exception, properties);

            // Set response
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            context.Response.ContentType = "application/json";

            var response = new
            {
                error = new
                {
                    message = "An internal server error occurred.",
                    statusCode = context.Response.StatusCode,
                    timestamp = DateTime.UtcNow,
                    traceId = traceId,
                    path = requestPath
                }
            };

            var jsonResponse = JsonConvert.SerializeObject(response);
            await context.Response.WriteAsync(jsonResponse);
        }
    }
}
