using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using OrderTracking.API.Models;
using OrderTracking.API.Repositories;

namespace OrderTracking.API.Services
{
    /// <summary>
    /// Azure Cosmos DB service for AuthHistory operations (migrated from Firebase)
    /// </summary>
    public class AuthHistoryCosmosService : IAuthHistoryService
    {
        private readonly Container _container;
        private readonly string _partitionKeyPath;

        public AuthHistoryCosmosService(IContainerFactory containerFactory)
        {
            if (containerFactory == null) throw new ArgumentNullException(nameof(containerFactory));
            _container = containerFactory.CreateCollection<AuthHistoryCosmosService>(out _partitionKeyPath);
        }

        public async Task<IEnumerable<ChangeEvent>> GetItemsAsync()
        {

            try
            {
                var query = new QueryDefinition("SELECT * FROM c ORDER BY c.createdAt DESC");

                var results = new List<ChangeEvent>();
                using var feedIterator = _container.GetItemQueryIterator<ChangeEvent>(query);

                while (feedIterator.HasMoreResults)
                {
                    var response = await feedIterator.ReadNextAsync();
                    results.AddRange(response.ToList());
                }

                return results.OrderByDescending(r => r.CreatedAt);
            }
            catch (CosmosException ex)
            {
                throw new InvalidOperationException($"Failed to retrieve auth history items: {ex.Message}", ex);
            }
            //catch (OutOfMemoryException ex)
            //{
            //    throw new InvalidOperationException("Too much data in auth history. Consider implementing pagination or filtering.", ex);
            //}
            //catch (Exception ex)
            //{
            //    throw new InvalidOperationException($"Unexpected error retrieving auth history: {ex.Message}", ex);
            //}
        }

        public async Task<ActionResult<ChangeEvent>> GetItemAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
                return null;

            try
            {
                var response = await _container.ReadItemAsync<ChangeEvent>(id, new PartitionKey(id));
                return response.Resource;
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return null;
            }
            catch (CosmosException ex)
            {
                throw new InvalidOperationException($"Failed to retrieve auth history item with id {id}: {ex.Message}", ex);
            }
        }

        public async Task<string> AddItemAsync(ChangeEvent changeEvent)
        {
            if (changeEvent == null) throw new ArgumentNullException(nameof(changeEvent));
            if (changeEvent.Old == null && changeEvent.New == null)
                throw new InvalidOperationException(
                    "Cannot create ChangeEvent without old and/or new snapshots of record being changed.");

            if (string.IsNullOrEmpty(changeEvent.Id))
            {
                changeEvent.Id = Guid.NewGuid().ToString();
            }

            // Ensure CreatedAt is set
            if (changeEvent.CreatedAt == default)
            {
                changeEvent.CreatedAt = DateTime.UtcNow;
            }

            try
            {
                var response = await _container.CreateItemAsync(changeEvent, new PartitionKey(changeEvent.Id));
                return response.Resource.Id;
            }
            catch (CosmosException ex)
            {
                throw new InvalidOperationException($"Failed to add auth history item: {ex.Message}", ex);
            }
        }

        public async Task RemoveAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
                return;

            try
            {
                await _container.DeleteItemAsync<ChangeEvent>(id, new PartitionKey(id));
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                // Item doesn't exist, nothing to delete
                return;
            }
            catch (CosmosException ex)
            {
                throw new InvalidOperationException($"Failed to remove auth history item with id {id}: {ex.Message}", ex);
            }
        }
    }
}
