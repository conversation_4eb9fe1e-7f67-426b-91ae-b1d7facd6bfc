# CORS Troubleshooting and Deployment Guide

## Issue: CORS Error in Container Apps Deployment

You're seeing the following error when your frontend app tries to access the backend API:

```
Access to XMLHttpRequest at 'https://clientportal-api-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io/api/users/<EMAIL>/' 
from origin 'https://clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io' has been blocked by CORS policy: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## Root Cause

The backend API is not configured to allow requests from your frontend Container App URL. Even though we've updated the CORS configuration in the code, the changes haven't been deployed yet.

## Solution Steps

### Option 1: Deploy Updated Backend API

1. Build the updated backend API:
   ```powershell
   # Navigate to API directory
   cd "c:\Users\<USER>\Desktop\GCP_PROJ\Kraken\api\OrderTracking.API"

   # Build the project
   dotnet build
   dotnet publish -c Release
   ```

2. Build and push the Docker image:
   ```powershell
   # Navigate to root project folder
   cd "c:\Users\<USER>\Desktop\GCP_PROJ\Kraken"

   # Build the Docker image
   docker build -t youracr.azurecr.io/testapi001:latest -f api/Dockerfiles/Dockerfile-cpa-backend .

   # Login to ACR
   az acr login --name youracr

   # Push the image
   docker push youracr.azurecr.io/testapi001:latest
   ```

3. Update the Container App:
   ```powershell
   # Update the container app with the new image
   az containerapp update --name testapi001 --resource-group YourResourceGroup --image youracr.azurecr.io/testapi001:latest
   ```

### Option 2: Configure CORS in Azure Portal

If you can't redeploy immediately, you can temporarily update CORS settings in the Azure portal:

1. Go to the Azure Portal
2. Navigate to your Container App (testapi001)
3. Under Settings, find "CORS" or "Application settings"
4. Add the following environment variable:
   ```
   Name: AllowedOrigins
   Value: https://clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io
   ```

### Option 3: Configure CORS via Azure CLI

```powershell
# Update container app with CORS settings
az containerapp update --name testapi001 --resource-group YourResourceGroup --env-vars "AllowedOrigins=https://clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io"
```

## Verifying the Fix

After applying one of the solutions above:

1. Restart your backend Container App
2. Clear your browser cache
3. Try accessing your frontend app again

## Additional Troubleshooting

If the issue persists:

1. **Check API Logs**: Look at the logs for your backend Container App in the Azure portal
2. **Test with curl**: Use curl to test CORS headers:
   ```
   curl -I -X OPTIONS -H "Origin: https://clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io" https://clientportal-api-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io/api/users/
   ```
3. **Check network traffic**: In Chrome DevTools, look at the Network tab for the request and check if CORS headers are present in the response

## The 500 Error

The secondary error (500 Internal Server Error) suggests there may be additional issues with the API. Once CORS is fixed, you may need to address this by:

1. Checking the API logs in Azure portal
2. Ensuring all connection strings and environment variables are properly configured
3. Verifying that the API can connect to any required databases or services
