using System.Data;
using System.Data.Common;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;

namespace ClientPortal.Shared.Extensions
{
    public static class SqlConnectionExtensions
    {
        public static async Task<DbDataReader> GetSourceReader(
            this DbConnection sourceConnection) =>
            await sourceConnection.ExecuteReaderAsync(
                @$" 
SELECT 
    CONVERT(NVARCHAR(254), ( 
                 HASHBYTES('SHA2_256', CONCAT( 
                    [CUSTPACKINGSLIPTRANS_RECID] 
                    ,'|',[CUSTPACKINGSLIPJOUR_RECID] 
                    ,'|',[INVENTDIM_INVENTDIMID] 
                    ,'|',[TIS_SALESPROD_RECID]  
                    ,'|',[PURCHTABLE_PURCHID]  
                    ,'|',[PROJTABLE_PROJID]  
                    ,'|',[PROJFUNDINGSOURCE_RECID]  
                    ,'|',[DIRPARTYTABLE_RECID]  
                    ,'|',[CUSTTABLE_ACCOUNTNUM]  
                    ,'|',[SALESLINE_INVENTTRANSID]  
                    ,'|',[SALESTABLE_SALESID]  
                    ,'|',[PRODID]  
                    ,'|',[JSSJOBID]
                    ,'|',ROW_NUMBER() OVER (PARTITION BY JSSJOBID ORDER BY JSSJOBID DESC)
                    ))), 1) AS [Id], 
    CONVERT (NVARCHAR(MAX),( 
                 HASHBYTES('SHA2_256', CONCAT ( 
                    [SALESLINEDATAAREAID] 
                    ,'|',[SALESID] 
                    ,'|',[PRODID] 
                    ,'|',[PROJID] 
                    ,'|',[SALESNAME] 
                    ,'|',[EXTERNALCUSTOMER] 
                    ,'|',[EXTERNALCUSTOMERNAME] 
                    ,'|',[PRICEUNIT] 
                    ,'|',[SALESLINEITEMID] 
                    ,'|',[NAME] 
                    ,'|',[SALESQTY] 
                    ,'|',[INTERNAL_CUSTOMERREF] 
                    ,'|',[INTERNAL_PURCHASEORDERFORMNUM] 
                    ,'|',[INTERCOMPANYPURCHID] 
                    ,'|',[JSSJOBID] 
                    ,'|',[JSSDATERECEIVED] 
                    ,'|',[JSSDATEDESIGNED] 
                    ,'|',[JSSDATECONVERTED] 
                    ,'|',[JSSDATEVERIFIED] 
                    ,'|',[JSSDATETOMFG] 
                    ,'|',[COLLECTREFPRODID] 
                    ,'|',[CREATEDDATETIME] 
                    ,'|',[SCHEDSTART] 
                    ,'|',[SCHEDEND] 
                    ,'|',[FINISHEDDATE] 
                    ,'|',[SHIPPINGDATEREQUESTED] 
                    ,'|',[SHIPPINGDATECONFIRMED] 
                    ,'|',[DELIVERYDATE] 
                    ,'|',[PACKINGSLIPID] 
                    ,'|',[SALESSTATUS] 
                    ,'|',[LINEAMOUNT] 
                    ,'|',[CUSTACCOUNT] 
                    ,'|',[TIS_TRACKINGNUMBER] 
                    ,'|',[INVENTSITEID] 
                    ,'|',[WMSLOCATIONID] 
                    ,'|',[INVENTLOCATIONID] 
                    ,'|',[ORDERED] 
                    ,'|',[DELIVERED] 
                    ,'|',[REMAININGQUANTITY] 
                    ,'|',[CUSTOMERREF] 
                    ,'|',[PURCHORDERFORMNUM] 
                    ,'|',[DISTRICTNUMBER] 
                    ,'|',[BILLINGCUSTOMERNAME] 
                    ,'|',[ROUTEID] 
                    ,'|',[REFERENCENUMBER] 
                    ,'|',[SALESLINE_SALESPRICE] 
                    ,'|',[SALESLINE_INVENTREFID] 
                    ,'|',[SALESLINE_INVENTTRANSID] 
                    ,'|',[SALESTABLE_SALESID] 
                    ,'|',[CUSTPACKINGSLIPTRANS_RECID] 
                    ,'|',[CUSTPACKINGSLIPJOUR_RECID] 
                    ,'|',[INVENTDIM_INVENTDIMID] 
                    ,'|',[TIS_SALESPROD_RECID] 
                    ,'|',[PURCHTABLE_PURCHID] 
                    ,'|',[TRI_JSSDELIVERYREMARKS] 
                    ,'|',[PROJTABLE_PROJID] 
                    ,'|',[PROJFUNDINGSOURCE_RECID] 
                    ,'|',[DIRPARTYTABLE_RECID] 
                    ,'|',[CUSTTABLE_ACCOUNTNUM] 
                    ,'|',[SHIPDATE] 
                    ,'|',[CONFIRMEDRECEIPTDATE] 
                    ,'|',[SALESLINE_RECID] 
                    ,'|',[PRODSERVICELINEDETAIL] 
                    ,'|',[PRODGROUPID] 
                    ,'|',[TIS_JSSROUTING] 
                    ,'|',[JSSDATESTAMPED] 
                    ,'|',[JSSRAPIDQUOTEREQUESTED] 
                    ,'|',[JSSRAPIDQUOTEAPPLICABLE] 
                    ,'|',[JSSRAPIDQUOTEDATE] 
                    ,'|',[JSSCOMPONENTID] 
                    ,'|',[OLDJSSVERIFIER] 
                    ,'|',[JSSPROFESSIONALENGINEER] 
                    ,'|',[JSSLANE] 
                    ,'|',[JSSPESTAMP] 
                    ,'|',[OLDJSSCURRENTHOLDER] 
                    ,'|',[JSSDATECUSTOMERETA] 
                    ,'|',[JSSDESCCOMPONENT] 
                    ,'|',[JSSJOBPRIORITY] 
                    ,'|',[JSSDATEENTERED] 
                    ,'|',[JSSDATECOMPLETED] 
                    ,'|',[JSSENCLOSUREMATERIAL] 
                    ,'|',[JSSLINESIZE] 
                    ,'|',[JSSLINEMATERIAL] 
                    ,'|',[JSSLEAKTYPE] 
                    ,'|',[JSSOD] 
                    ,'|',[JSSDESIGNPRESSURE] 
                    ,'|',[JSSDESIGNTEMPERATURE] 
                    ,'|',[JSSENCLOSURESEAL] 
                    ,'|',[JSSBRANCH] 
                    ,'|',[CUSTNAME] 
                    ,'|',[PRODServiceLineGroup] 
                    ,'|',[JSSENGINEERINGPRIORITY] 
                    ,'|',[JSSJobsTableRepairType] 
                    ,'|',[JSSSpecTableRepairType] 
                    ,'|',[JSSJOBSTATUS] 
                    ,'|',[JSSMODIFIEDDATETIME] 
                    ,'|',[JSSMODIFIEDBY] 
                    ,'|',[OldJSSDesigner] 
                    ,'|',[DataTaker] 
                    ,'|',[JSSTier]
                    ,'|',[JSSSERVICELINE]
                    ,'|',[JSSSERVICELINEGROUP]
                    ,'|',ROW_NUMBER() OVER (PARTITION BY JSSJOBID ORDER BY JSSJOBID DESC)
                    ))),1) as [HASH], 
    [SALESLINEDATAAREAID], 
    [SALESID], 
    [PRODID], 
    [PROJID], 
    [SALESNAME], 
    [EXTERNALCUSTOMER], 
    [EXTERNALCUSTOMERNAME], 
    [PRICEUNIT], 
    [SALESLINEITEMID], 
    [NAME], 
    [SALESQTY], 
    [INTERNAL_CUSTOMERREF], 
    [INTERNAL_PURCHASEORDERFORMNUM], 
    [INTERCOMPANYPURCHID], 
    [JSSJOBID], 
    ( 
      SELECT CASE 
      WHEN JSSDATERECEIVED = '01/01/1900' THEN null 
	    ELSE JSSDATERECEIVED 
	    END 
	  ) AS [JSSDATERECEIVED], 
    ( 
      SELECT CASE 
      WHEN JSSDATEDESIGNED = '01/01/1900' THEN null 
	    ELSE JSSDATEDESIGNED 
	    END 
	  ) AS [JSSDATEDESIGNED], 
    ( 
      SELECT CASE 
      WHEN JSSDATECONVERTED = '01/01/1900' THEN null 
	    ELSE JSSDATECONVERTED 
	    END 
	  ) AS [JSSDATECONVERTED], 
    ( 
      SELECT CASE 
      WHEN JSSDATEVERIFIED = '01/01/1900' THEN null 
	    ELSE JSSDATEVERIFIED 
	    END 
	  ) AS [JSSDATEVERIFIED], 
    ( 
      SELECT CASE 
      WHEN JSSDATETOMFG = '01/01/1900' THEN null 
	    ELSE JSSDATETOMFG 
	    END 
	  ) AS [JSSDATETOMFG], 
    [COLLECTREFPRODID], 
    ( 
      SELECT CASE 
      WHEN CREATEDDATETIME = '01/01/1900' THEN null 
	    ELSE CREATEDDATETIME 
	    END 
	  ) AS [CREATEDDATETIME], 
    ( 
      SELECT CASE 
      WHEN SCHEDSTART = '01/01/1900' THEN null 
	    ELSE SCHEDSTART 
	    END 
	  ) AS [SCHEDSTART], 
    ( 
      SELECT CASE 
      WHEN SCHEDEND = '01/01/1900' THEN null 
	    ELSE SCHEDEND 
	    END 
	  ) AS [SCHEDEND], 
    ( 
      SELECT CASE 
      WHEN FINISHEDDATE = '01/01/1900' THEN null 
	    ELSE FINISHEDDATE 
	    END 
	  ) AS [FINISHEDDATE], 
    [SHIPPINGDATEREQUESTED], 
   ( 
      SELECT CASE 
      WHEN SHIPPINGDATECONFIRMED = '01/01/1900' THEN null 
	  ELSE SHIPPINGDATECONFIRMED 
	  END 
	) AS [SHIPPINGDATECONFIRMED], 
    ( 
      SELECT CASE 
      WHEN DELIVERYDATE = '01/01/1900' THEN null 
	    ELSE DELIVERYDATE 
	    END 
	  ) AS [DELIVERYDATE], 
    [PACKINGSLIPID], 
    [SALESSTATUS], 
    [LINEAMOUNT], 
    [CUSTACCOUNT], 
    [TIS_TRACKINGNUMBER], 
    [INVENTSITEID], 
    [WMSLOCATIONID], 
    ( 
        SELECT CASE  
        WHEN LOWER(INVENTLOCATIONID) = 'main' THEN 'Alvin' 
        ELSE INVENTLOCATIONID 
        END 
    ) AS [INVENTLOCATIONID], 
    [ORDERED], 
    [DELIVERED], 
    [REMAININGQUANTITY], 
    [CUSTOMERREF], 
    [PURCHORDERFORMNUM], 
    [DISTRICTNUMBER], 
    [BILLINGCUSTOMERNAME], 
    [ROUTEID], 
    [REFERENCENUMBER], 
    [SALESLINE_SALESPRICE], 
    [SALESLINE_INVENTREFID], 
    [SALESLINE_INVENTTRANSID], 
    [SALESTABLE_SALESID], 
    [CUSTPACKINGSLIPTRANS_RECID], 
    [CUSTPACKINGSLIPJOUR_RECID], 
    [INVENTDIM_INVENTDIMID], 
    [TIS_SALESPROD_RECID], 
    [PURCHTABLE_PURCHID], 
    [PROJTABLE_PROJID], 
    [PROJFUNDINGSOURCE_RECID], 
    [DIRPARTYTABLE_RECID], 
    [CUSTTABLE_ACCOUNTNUM], 
    CASE 
      WHEN ISNULL(NULLIF(LTRIM(RTRIM(INTERCOMPANYPURCHID)), ''), NULL) IS NOT NULL THEN SUBSTRING([INTERCOMPANYPURCHID],1,4) 
      ELSE SUBSTRING(PROJID, 1, 4) 
    END AS INTERNALDISTRICTNUMBER, 
    ( 
        SELECT CASE  
        WHEN LOWER(SALESSTATUS) = 'invoiced' THEN 'Delivered' 
        WHEN DELIVERYDATE IS NOT NULL THEN 'Shipped' 
        WHEN CREATEDDATETIME IS NOT NULL AND DELIVERYDATE IS NULL THEN 'Production' 
        WHEN NULLIF(LTRIM(RTRIM(JSSJOBID)), '') IS NOT NULL AND DELIVERYDATE IS NULL AND CREATEDDATETIME IS NULL THEN 'Engineering' 
        WHEN NULLIF(LTRIM(RTRIM(INTERCOMPANYPURCHID)), '') IS NOT NULL THEN 'Ordered' 
        ELSE NULL 
        END 
    ) AS 'STATUS', 
    ( 
      SELECT CASE 
      WHEN SHIPDATE = '01/01/1900' THEN null 
	    ELSE SHIPDATE 
	    END 
	  ) AS SHIPDATE, 
       ( 
      SELECT CASE 
        WHEN CONFIRMEDRECEIPTDATE = '01/01/1900' THEN null 
	    ELSE CONFIRMEDRECEIPTDATE 
	    END 
	  ) AS CONFIRMEDRECEIPTDATE, 
    [TRI_JSSDELIVERYREMARKS], 
    (  
        SELECT CASE 
        WHEN [SALESLINE_RECID] IS NULL THEN -1 
        ELSE [SALESLINE_RECID] 
        END 
    ), 
    [PRODSERVICELINEDETAIL], 
    [PRODGROUPID], 
    [TIS_JSSROUTING], 
    [JSSDATESTAMPED], 
    [JSSRAPIDQUOTEREQUESTED], 
    [JSSRAPIDQUOTEAPPLICABLE], 
    [JSSRAPIDQUOTEDATE], 
    [JSSCOMPONENTID], 
    [OLDJSSVERIFIER], 
    [JSSPROFESSIONALENGINEER], 
    [JSSLANE], 
    [JSSPESTAMP], 
    [OLDJSSCURRENTHOLDER], 
    [JSSDATECUSTOMERETA], 
    [JSSDESCCOMPONENT], 
    [JSSJOBPRIORITY], 
    [JSSDATEENTERED], 
    [JSSDATECOMPLETED], 
    [JSSENCLOSUREMATERIAL], 
    [JSSLINESIZE], 
    [JSSLINEMATERIAL], 
    [JSSLEAKTYPE], 
    [JSSOD], 
    [JSSDESIGNPRESSURE], 
    [JSSDESIGNTEMPERATURE], 
    [JSSENCLOSURESEAL], 
    [JSSBRANCH], 
    [CUSTNAME], 
    [PRODServiceLineGroup], 
    [JSSENGINEERINGPRIORITY], 
    [JSSJobsTableRepairType], 
    [JSSSpecTableRepairType], 
    [JSSJOBSTATUS], 
    [JSSMODIFIEDDATETIME], 
    [JSSMODIFIEDBY], 
    [OldJSSDesigner], 
    [DataTaker], 
    [JSSTier],
    [JSSSERVICELINE],
    [JSSSERVICELINEGROUP]
FROM [{Constants.SourceDatabaseName}].[{Constants.SourceDatabaseSchemaName}].[{Constants.SourceTableName}] 
WHERE NOT ( 
    CUSTPACKINGSLIPTRANS_RECID IS NULL and  
    CUSTPACKINGSLIPJOUR_RECID IS NULL and  
    INVENTDIM_INVENTDIMID IS NULL and  
    TIS_SALESPROD_RECID IS NULL and  
    PURCHTABLE_PURCHID IS NULL and  
    PROJTABLE_PROJID IS NULL and  
    PROJFUNDINGSOURCE_RECID IS NULL and  
    DIRPARTYTABLE_RECID IS NULL and  
    CUSTTABLE_ACCOUNTNUM IS NULL and  
    SALESLINE_INVENTTRANSID IS NULL and  
    SALESTABLE_SALESID IS NULL and  
    PRODID IS NULL and  
    JSSJOBID IS NULL 
)");

        /// <summary> 
        ///     Create SqlBulkCopy object. 
        /// </summary> 
        /// <param name="connection"></param> 
        /// <param name="tableName"></param> 
        /// <param name="logger">include an ILogger if you want to log out row copy events</param> 
        /// <returns></returns> 
        public static SqlBulkCopy CreateSqlBulkCopy(this SqlConnection connection, string tableName, ILogger logger = null)
        {
            // TODO: Should we use TableLock in the SqlBulkCopyOptions? 
            var bulkCopy = new SqlBulkCopy(connection)
            {
                DestinationTableName = tableName,
                BatchSize = 1000, // Googled this and 500-1000 seems to be a good choice 
                EnableStreaming = true,
                BulkCopyTimeout = 300,
                NotifyAfter = 5000
            };

            if (logger != null)
                bulkCopy.SqlRowsCopied += (sender, args) => logger.LogInformation($"{args.RowsCopied} rows copied");

            return bulkCopy;
        }

        public static async Task<bool> TableExistsAsync(this IDbConnection targetConnection, string tableName) =>
            await targetConnection.ExecuteScalarAsync(
                $"SELECT OBJECT_ID('{tableName}')") !=
            null;

        public static async Task TruncateTableAsync(this IDbConnection targetConnection,
            string tableName)
        {
            await targetConnection.ExecuteAsync($"TRUNCATE TABLE [{tableName}]");
        }

        public static async Task CreateOrdersJobsTableAsync(this IDbConnection targetConnection,
            string tableName = "OrdersJobs")
        {
            await targetConnection.ExecuteAsync($@" 
CREATE TABLE [{tableName}] ( 
	[Id]            [nvarchar](254) NOT NULL, 
	[Status]        [nvarchar](10)  NOT NULL, 
	[StartedUTC]    [datetime]      NOT NULL, 
	[EndedUTC]      [datetime]      NOT NULL, 
	[Inserted]      [int]           NOT NULL, 
	[Updated]       [int]           NOT NULL, 
	[Deleted]       [int]           NOT NULL 
);");
        }

        public static async Task CreateOrdersTableAsync(
            this IDbConnection targetConnection, string tableName = Constants.TargetTableName)
        {
            await targetConnection.ExecuteAsync(@$" 
CREATE TABLE [{tableName}] ( 
    [HASH]                          NVARCHAR (MAX)   NOT NULL, 
    [Id]                            NVARCHAR (254)   NOT NULL, 
    [SALESLINEDATAAREAID]           NVARCHAR (4)     NULL, 
    [SALESID]                       NVARCHAR (20)    NULL, 
    [PRODID]                        NVARCHAR (20)    NULL, 
    [PROJID]                        NVARCHAR (20)    NULL, 
    [SALESNAME]                     NVARCHAR (60)    NULL, 
    [EXTERNALCUSTOMER]              NVARCHAR (20)    NULL, 
    [EXTERNALCUSTOMERNAME]          NVARCHAR (100)   NULL, 
    [PRICEUNIT]                     NUMERIC (32, 16) NULL, 
    [SALESLINEITEMID]               NVARCHAR (20)    NULL, 
    [NAME]                          NVARCHAR (1000)  NULL, 
    [SALESQTY]                      NUMERIC (32, 16) NULL, 
    [INTERNAL_CUSTOMERREF]          NVARCHAR (60)    NULL, 
    [INTERNAL_PURCHASEORDERFORMNUM] NVARCHAR (20)    NULL, 
    [INTERCOMPANYPURCHID]           NVARCHAR (20)    NULL, 
    [JSSJOBID]                      NVARCHAR (20)    NULL, 
    [JSSDATERECEIVED]               DATETIME         NULL, 
    [JSSDATEDESIGNED]               DATETIME         NULL, 
    [JSSDATECONVERTED]              DATETIME         NULL, 
    [JSSDATEVERIFIED]               DATETIME         NULL, 
    [JSSDATETOMFG]                  DATETIME         NULL, 
    [COLLECTREFPRODID]              NVARCHAR (20)    NULL, 
    [CREATEDDATETIME]               DATETIME         NULL, 
    [SCHEDSTART]                    DATETIME         NULL, 
    [SCHEDEND]                      DATETIME         NULL, 
    [FINISHEDDATE]                  DATETIME         NULL, 
    [SHIPPINGDATEREQUESTED]         DATETIME         NULL, 
    [SHIPPINGDATECONFIRMED]         DATETIME         NULL, 
    [DELIVERYDATE]                  DATETIME         NULL, 
    [PACKINGSLIPID]                 NVARCHAR (20)    NULL, 
    [SALESSTATUS]                   NVARCHAR (20)    NULL, 
    [LINEAMOUNT]                    NUMERIC (32, 16) NULL, 
    [CUSTACCOUNT]                   NVARCHAR (20)    NULL, 
    [TIS_TRACKINGNUMBER]            NVARCHAR (60)    NULL, 
    [INVENTSITEID]                  NVARCHAR (60)    NULL, 
    [WMSLOCATIONID]                 NVARCHAR (10)    NULL, 
    [INVENTLOCATIONID]              NVARCHAR (10)    NULL, 
    [ORDERED]                       NUMERIC (32, 16) NULL, 
    [DELIVERED]                     NUMERIC (32, 16) NULL, 
    [REMAININGQUANTITY]             NUMERIC (32, 16) NULL, 
    [CUSTOMERREF]                   NVARCHAR (60)    NULL, 
    [PURCHORDERFORMNUM]             NVARCHAR (20)    NULL, 
    [DISTRICTNUMBER]                NVARCHAR (4)     NULL, 
    [BILLINGCUSTOMERNAME]           NVARCHAR (20)    NULL, 
    [ROUTEID]                       NVARCHAR (20)    NULL, 
    [REFERENCENUMBER]               NVARCHAR (20)    NULL, 
    [SALESLINE_SALESPRICE]          NUMERIC (32, 16) NULL, 
    [SALESLINE_INVENTREFID]         NVARCHAR (20)    NULL, 
    [SALESLINE_INVENTTRANSID]       NVARCHAR (20)    NULL, 
    [SALESTABLE_SALESID]            NVARCHAR (20)    NULL, 
    [CUSTPACKINGSLIPTRANS_RECID]    BIGINT           NULL, 
    [CUSTPACKINGSLIPJOUR_RECID]     BIGINT           NULL, 
    [INVENTDIM_INVENTDIMID]         NVARCHAR (20)    NULL, 
    [TIS_SALESPROD_RECID]           BIGINT           NULL, 
    [PURCHTABLE_PURCHID]            NVARCHAR (20)    NULL, 
    [PROJTABLE_PROJID]              NVARCHAR (20)    NULL, 
    [PROJFUNDINGSOURCE_RECID]       BIGINT           NULL, 
    [DIRPARTYTABLE_RECID]           BIGINT           NULL, 
    [CUSTTABLE_ACCOUNTNUM]          NVARCHAR (20)    NULL, 
    [INTERNALDISTRICTNUMBER]        NVARCHAR (10)    NULL,  
    [STATUS]                        NVARCHAR (20)    NULL, 
    [SHIPDATE]                      DATETIME         NULL, 
    [CONFIRMEDRECEIPTDATE]          DATETIME         NULL, 
    [TRI_JSSDELIVERYREMARKS]        NVARCHAR (60)    NULL, 
    [SALESLINE_RECID]               BIGINT           NOT NULL, 
    [PRODSERVICELINEDETAIL]         NVARCHAR (20)    NULL, 
    [PRODGROUPID]                   NVARCHAR (20)    NULL, 
    [TIS_JSSROUTING]                nvarchar (60)    NULL, 
    [JSSDATESTAMPED]                datetime         NULL, 
    [JSSRAPIDQUOTEREQUESTED]        nvarchar (10)    NULL, 
    [JSSRAPIDQUOTEAPPLICABLE]       nvarchar (10)    NULL, 
    [JSSRAPIDQUOTEDATE]             datetime         NULL, 
    [JSSCOMPONENTID]                nvarchar (60)    NULL, 
    [OLDJSSVERIFIER]                nvarchar (100)   NULL, 
    [JSSPROFESSIONALENGINEER]       nvarchar (100)   NULL, 
    [JSSLANE]                       nvarchar (10)    NULL, 
    [JSSPESTAMP]                    nvarchar (10)    NULL, 
    [OLDJSSCURRENTHOLDER]           nvarchar (100)   NULL, 
    [JSSDATECUSTOMERETA]            datetime         NULL, 
    [JSSDESCCOMPONENT]              nvarchar (250)   NULL, 
    [JSSJOBPRIORITY]                nvarchar (30)    NULL, 
    [JSSDATEENTERED]                datetime         NULL, 
    [JSSDATECOMPLETED]              datetime         NULL, 
    [JSSENCLOSUREMATERIAL]          nvarchar (30)    NULL, 
    [JSSLINESIZE]                   nvarchar (30)    NULL, 
    [JSSLINEMATERIAL]               nvarchar (30)    NULL, 
    [JSSLEAKTYPE]                   nvarchar (30)    NULL, 
    [JSSOD]                         numeric (32, 16) NULL, 
    [JSSDESIGNPRESSURE]             numeric (32, 16) NULL, 
    [JSSDESIGNTEMPERATURE]          numeric (32, 16) NULL, 
    [JSSENCLOSURESEAL]              nvarchar (30)    NULL, 
    [JSSBRANCH]                     nvarchar (4)     NULL, 
    [CUSTNAME]                      nvarchar (60)    NULL, 
    [PRODServiceLineGroup]          nvarchar (10)    NULL, 
    [JSSENGINEERINGPRIORITY]        nvarchar (30)    NULL, 
    [JSSJobsTableRepairType]        nvarchar (30)    NULL, 
    [JSSSpecTableRepairType]        nvarchar (30)    NULL, 
    [JSSJOBSTATUS]                  nvarchar (10)    NULL, 
    [JSSMODIFIEDDATETIME]           datetime         NULL, 
    [JSSMODIFIEDBY]                 nvarchar (100)   NULL, 
    [OldJSSDesigner]                nvarchar (100)   NULL, 
    [DataTaker]                     nvarchar (60)    NULL, 
    [JSSTier]                       nvarchar (10)    NULL,
    [JSSSERVICELINE]                nvarchar (10)    NULL,
    [JSSSERVICELINEGROUP]           nvarchar (10)    NULL
    CONSTRAINT [PK_{tableName}] PRIMARY KEY ([Id])  
);");
        }
    }
}