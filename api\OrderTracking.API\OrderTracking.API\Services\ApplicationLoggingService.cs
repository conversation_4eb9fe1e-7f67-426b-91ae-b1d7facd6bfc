using Microsoft.ApplicationInsights;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;

namespace OrderTracking.API.Services
{
    /// <summary>
    /// Service for structured logging using Microsoft.Extensions.Logging with Application Insights integration
    /// </summary>
    public interface IApplicationLoggingService
    {
        void LogInformation(string message, params object[] args);
        void LogInformation<T>(string message, T state) where T : class;
        void LogWarning(string message, params object[] args);
        void LogWarning<T>(string message, T state) where T : class;
        void LogError(Exception exception, string message, params object[] args);
        void LogError<T>(Exception exception, string message, T state) where T : class;
        void LogError(string message, params object[] args);
        void TrackEvent(string eventName, Dictionary<string, string> properties = null, Dictionary<string, double> metrics = null);
        void TrackDependency(string dependencyType, string dependencyName, string data, DateTime startTime, TimeSpan duration, bool success);
        void TrackUserAction(string userId, string action, string resource, bool success, object additionalData = null);
        IDisposable BeginScope<TState>(TState state);
    }

    public class ApplicationLoggingService : IApplicationLoggingService
    {
        private readonly ILogger<ApplicationLoggingService> _logger;
        private readonly TelemetryClient _telemetryClient;

        public ApplicationLoggingService(ILogger<ApplicationLoggingService> logger, TelemetryClient telemetryClient)
        {
            _logger = logger;
            _telemetryClient = telemetryClient;
        }

        public void LogInformation(string message, params object[] args)
        {
            _logger.LogInformation(message, args);
        }

        public void LogInformation<T>(string message, T state) where T : class
        {
            using var scope = _logger.BeginScope(state);
            _logger.LogInformation(message);
            
            // Track as custom event in Application Insights
            var properties = ConvertToProperties(state);
            properties.TryAdd("Message", message);
            _telemetryClient.TrackEvent("Information", properties);
        }

        public void LogWarning(string message, params object[] args)
        {
            _logger.LogWarning(message, args);
        }

        public void LogWarning<T>(string message, T state) where T : class
        {
            using var scope = _logger.BeginScope(state);
            _logger.LogWarning(message);
            
            // Track as custom event in Application Insights
            var properties = ConvertToProperties(state);
            properties.TryAdd("Message", message);
            _telemetryClient.TrackEvent("Warning", properties);
        }

        public void LogError(Exception exception, string message, params object[] args)
        {
            _logger.LogError(exception, message, args);
            
            // Track exception in Application Insights
            var properties = new Dictionary<string, string>
            {
                { "Message", message },
                { "FormattedMessage", string.Format(message, args) }
            };
            
            _telemetryClient.TrackException(exception, properties);
        }

        public void LogError<T>(Exception exception, string message, T state) where T : class
        {
            using var scope = _logger.BeginScope(state);
            _logger.LogError(exception, message);
            
            // Track exception in Application Insights with structured data
            var properties = ConvertToProperties(state);
            properties.TryAdd("Message", message);
            
            _telemetryClient.TrackException(exception, properties);
        }

        public void LogError(string message, params object[] args)
        {
            _logger.LogError(message, args);
            
            // Track as custom event in Application Insights
            var properties = new Dictionary<string, string>
            {
                { "Message", message },
                { "FormattedMessage", string.Format(message, args) },
                { "Level", "Error" }
            };
            
            _telemetryClient.TrackEvent("Error", properties);
        }

        public void TrackEvent(string eventName, Dictionary<string, string> properties = null, Dictionary<string, double> metrics = null)
        {
            _logger.LogInformation("Custom Event: {EventName}", eventName);
            _telemetryClient.TrackEvent(eventName, properties, metrics);
        }

        public void TrackDependency(string dependencyType, string dependencyName, string data, DateTime startTime, TimeSpan duration, bool success)
        {
            _logger.LogInformation(
                "Dependency Call: {DependencyType}.{DependencyName} - Duration: {Duration}ms, Success: {Success}",
                dependencyType, dependencyName, duration.TotalMilliseconds, success);
                
            _telemetryClient.TrackDependency(dependencyType, dependencyName, data, startTime, duration, success);
        }

        public void TrackUserAction(string userId, string action, string resource, bool success, object additionalData = null)
        {
            var properties = new Dictionary<string, string>
            {
                { "UserId", userId },
                { "Action", action },
                { "Resource", resource },
                { "Success", success.ToString() },
                { "Timestamp", DateTime.UtcNow.ToString("O") }
            };

            if (additionalData != null)
            {
                var additionalProperties = ConvertToProperties(additionalData);
                foreach (var prop in additionalProperties)
                {
                    properties.TryAdd($"Additional_{prop.Key}", prop.Value);
                }
            }

            using var scope = _logger.BeginScope(properties);
            _logger.LogInformation(
                "User Action: {UserId} performed {Action} on {Resource} - Success: {Success}",
                userId, action, resource, success);

            _telemetryClient.TrackEvent("UserAction", properties);
        }

        public IDisposable BeginScope<TState>(TState state)
        {
            return _logger.BeginScope(state);
        }

        private Dictionary<string, string> ConvertToProperties(object data)
        {
            var properties = new Dictionary<string, string>();
            
            if (data == null) return properties;

            try
            {
                var type = data.GetType();
                var props = type.GetProperties();

                foreach (var prop in props)
                {
                    try
                    {
                        var value = prop.GetValue(data);
                        properties.TryAdd(prop.Name, value?.ToString() ?? "null");
                    }
                    catch (Exception ex)
                    {
                        properties.TryAdd(prop.Name, $"Error reading property: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                properties.TryAdd("SerializationError", ex.Message);
            }

            return properties;
        }
    }
}
