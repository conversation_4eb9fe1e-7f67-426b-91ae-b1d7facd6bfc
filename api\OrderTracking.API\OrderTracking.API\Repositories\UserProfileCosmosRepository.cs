using ClientPortal.Shared.Models;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    /// Azure Cosmos DB repository for UserProfile entities (migrated from Firebase)
    /// </summary>
    public class UserProfileCosmosRepository : BaseCosmosRepository<UserProfile, string>, IUserProfileRepository
    {
        #region Constructors

        public UserProfileCosmosRepository(IContainerFactory containerFactory, IConfiguration configuration) 
            : base(containerFactory.CreateCollection<UserProfileCosmosRepository>(out var partitionKeyPath), partitionKeyPath)
        {
        }

        public UserProfileCosmosRepository(Container container, string partitionKeyPath)
            : base(container, partitionKeyPath)
        {
        }

        #endregion

        #region Interface Implementation

        public override async Task<UserProfile> GetAsync(string entityId) => await GetAsync(entityId, entityId);

        public override async Task<UserProfile> UpdateAsync(UserProfile entity, string originalId)
        {
            try
            {
                // If the ID changed, we need to delete the old item and create a new one
                if (entity.Id != originalId)
                {
                    await RemoveAsync(originalId, originalId);
                    return await AddAsync(entity);
                }
                else
                {
                    return await UpdateAsync(entity);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public override async Task RemoveAsync(string id, string partitionId)
        {
            try
            {
                await Container.DeleteItemAsync<UserProfile>(id, new PartitionKey(partitionId));
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                // Item doesn't exist, which is fine for a delete operation
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<IEnumerable<UserProfile>> GetUsersForGroupAsync(string group)
        {
            try
            {
                var queryDefinition = new QueryDefinition("SELECT * FROM c WHERE ARRAY_CONTAINS(c.Groups, @group)")
                    .WithParameter("@group", group);

                var query = Container.GetItemQueryIterator<UserProfile>(queryDefinition);
                var results = new List<UserProfile>();

                while (query.HasMoreResults)
                {
                    var response = await query.ReadNextAsync();
                    results.AddRange(response.ToList());
                }

                return results;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<IEnumerable<UserProfile>> GetUsersForRoleAsync(string role)
        {
            try
            {
                // Primary approach: Case-insensitive search using EXISTS and UPPER
                var queryDefinition = new QueryDefinition(
                    "SELECT * FROM c WHERE EXISTS(SELECT VALUE r FROM r IN c.roles WHERE UPPER(r) = UPPER(@role))")
                    .WithParameter("@role", role);

                var query = Container.GetItemQueryIterator<UserProfile>(queryDefinition);
                var results = new List<UserProfile>();

                while (query.HasMoreResults)
                {
                    var response = await query.ReadNextAsync();
                    results.AddRange(response.ToList());
                }

                Console.WriteLine($"Found {results.Count} users with role '{role}' using case-insensitive search");

                // Fallback: If no results found, try exact match (original approach)
                if (results.Count == 0)
                {
                    Console.WriteLine($"No results with case-insensitive search, trying exact match for role '{role}'");

                    var exactQueryDefinition = new QueryDefinition("SELECT * FROM c WHERE ARRAY_CONTAINS(c.roles, @role)")
                        .WithParameter("@role", role);

                    var exactQuery = Container.GetItemQueryIterator<UserProfile>(exactQueryDefinition);

                    while (exactQuery.HasMoreResults)
                    {
                        var response = await exactQuery.ReadNextAsync();
                        results.AddRange(response.ToList());
                    }

                    Console.WriteLine($"Found {results.Count} users with exact role match '{role}'");
                }

                return results;
            }
            catch (Exception e)
            {
                Console.WriteLine($"GetUsersForRoleAsync failed for role '{role}': {e}");
                throw;
            }
        }

        #endregion
    }
}
