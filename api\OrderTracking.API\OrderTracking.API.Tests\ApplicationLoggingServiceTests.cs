//using Microsoft.ApplicationInsights;
//using Microsoft.Extensions.Logging;
//using Moq;
//using OrderTracking.API.Services;
//using System;
//using System.Collections.Generic;
//using Xunit;

//namespace OrderTracking.API.Tests
//{
//    public class ApplicationLoggingServiceTests
//    {
//        private readonly Mock<ILogger<ApplicationLoggingService>> _mockLogger;
//        private readonly Mock<TelemetryClient> _mockTelemetryClient;
//        private readonly ApplicationLoggingService _service;

//        public ApplicationLoggingServiceTests()
//        {
//            _mockLogger = new Mock<ILogger<ApplicationLoggingService>>();
//            _mockTelemetryClient = new Mock<TelemetryClient>();
//            _service = new ApplicationLoggingService(_mockLogger.Object, _mockTelemetryClient.Object);
//        }

//        [Fact]
//        public void LogInformation_WithMessage_CallsLogger()
//        {
//            // Arrange
//            var message = "Test information message";

//            // Act
//            _service.LogInformation(message);

//            // Assert
//            _mockLogger.Verify(
//                x => x.Log(
//                    LogLevel.Information,
//                    It.IsAny<EventId>(),
//                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains(message)),
//                    It.IsAny<Exception>(),
//                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//                Times.Once);
//        }

//        [Fact]
//        public void LogInformation_WithState_CallsLoggerAndTelemetry()
//        {
//            // Arrange
//            var message = "Test information with state";
//            var state = new { UserId = "123", Action = "Login" };

//            // Act
//            _service.LogInformation(message, state);

//            // Assert
//            _mockLogger.Verify(
//                x => x.Log(
//                    LogLevel.Information,
//                    It.IsAny<EventId>(),
//                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains(message)),
//                    It.IsAny<Exception>(),
//                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//                Times.Once);

//            _mockTelemetryClient.Verify(
//                x => x.TrackEvent("Information", It.IsAny<Dictionary<string, string>>(), null),
//                Times.Once);
//        }

//        [Fact]
//        public void LogError_WithException_CallsLoggerAndTrackException()
//        {
//            // Arrange
//            var exception = new Exception("Test exception");
//            var message = "Test error message";

//            // Act
//            _service.LogError(exception, message);

//            // Assert
//            _mockLogger.Verify(
//                x => x.Log(
//                    LogLevel.Error,
//                    It.IsAny<EventId>(),
//                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains(message)),
//                    exception,
//                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//                Times.Once);

//            _mockTelemetryClient.Verify(
//                x => x.TrackException(exception, It.IsAny<Dictionary<string, string>>()),
//                Times.Once);
//        }

//        [Fact]
//        public void LogError_WithExceptionAndState_CallsLoggerAndTrackException()
//        {
//            // Arrange
//            var exception = new Exception("Test exception");
//            var message = "Test error with state";
//            var state = new { UserId = "123", Operation = "DataAccess" };

//            // Act
//            _service.LogError(exception, message, state);

//            // Assert
//            _mockLogger.Verify(
//                x => x.Log(
//                    LogLevel.Error,
//                    It.IsAny<EventId>(),
//                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains(message)),
//                    exception,
//                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//                Times.Once);

//            _mockTelemetryClient.Verify(
//                x => x.TrackException(exception, It.IsAny<Dictionary<string, string>>()),
//                Times.Once);
//        }

//        [Fact]
//        public void LogWarning_WithMessage_CallsLogger()
//        {
//            // Arrange
//            var message = "Test warning message";

//            // Act
//            _service.LogWarning(message);

//            // Assert
//            _mockLogger.Verify(
//                x => x.Log(
//                    LogLevel.Warning,
//                    It.IsAny<EventId>(),
//                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains(message)),
//                    It.IsAny<Exception>(),
//                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//                Times.Once);
//        }

//        [Fact]
//        public void TrackUserAction_CallsLoggerAndTelemetry()
//        {
//            // Arrange
//            var userId = "user123";
//            var action = "ViewOrder";
//            var resource = "Order/123";
//            var success = true;
//            var additionalData = new { OrderId = "123", Amount = 100.50 };

//            // Act
//            _service.TrackUserAction(userId, action, resource, success, additionalData);

//            // Assert
//            _mockLogger.Verify(
//                x => x.Log(
//                    LogLevel.Information,
//                    It.IsAny<EventId>(),
//                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("User Action")),
//                    It.IsAny<Exception>(),
//                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//                Times.Once);

//            _mockTelemetryClient.Verify(
//                x => x.TrackEvent("UserAction", It.IsAny<Dictionary<string, string>>(), null),
//                Times.Once);
//        }

//        [Fact]
//        public void TrackEvent_CallsLoggerAndTelemetry()
//        {
//            // Arrange
//            var eventName = "CustomEvent";
//            var properties = new Dictionary<string, string> { { "Key1", "Value1" } };
//            var metrics = new Dictionary<string, double> { { "Metric1", 1.5 } };

//            // Act
//            _service.TrackEvent(eventName, properties, metrics);

//            // Assert
//            _mockLogger.Verify(
//                x => x.Log(
//                    LogLevel.Information,
//                    It.IsAny<EventId>(),
//                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Custom Event")),
//                    It.IsAny<Exception>(),
//                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//                Times.Once);

//            _mockTelemetryClient.Verify(
//                x => x.TrackEvent(eventName, properties, metrics),
//                Times.Once);
//        }

//        [Fact]
//        public void TrackDependency_CallsLoggerAndTelemetry()
//        {
//            // Arrange
//            var dependencyType = "SQL";
//            var dependencyName = "GetOrders";
//            var data = "SELECT * FROM Orders";
//            var startTime = DateTime.UtcNow.AddSeconds(-5);
//            var duration = TimeSpan.FromSeconds(2);
//            var success = true;

//            // Act
//            _service.TrackDependency(dependencyType, dependencyName, data, startTime, duration, success);

//            // Assert
//            _mockLogger.Verify(
//                x => x.Log(
//                    LogLevel.Information,
//                    It.IsAny<EventId>(),
//                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Dependency Call")),
//                    It.IsAny<Exception>(),
//                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//                Times.Once);

//            _mockTelemetryClient.Verify(
//                x => x.TrackDependency(dependencyType, dependencyName, data, startTime, duration, success),
//                Times.Once);
//        }

//        [Fact]
//        public void BeginScope_CallsLoggerBeginScope()
//        {
//            // Arrange
//            var state = new { CorrelationId = "123", UserId = "user456" };

//            // Act
//            using var scope = _service.BeginScope(state);

//            // Assert
//            _mockLogger.Verify(
//                x => x.BeginScope(state),
//                Times.Once);
//        }
//    }
//}
