{"ConnectionStrings": {"Source": "Server=.;Initial Catalog=orders-dump;Integrated Security=True", "Target": "Server=.;Initial Catalog=orders-devteststg;Integrated Security=True"}, "ApplicationInsights": {"InstrumentationKey": "fc63cc35-c684-42fb-9442-055e806fbaf6"}, "ZDapperPlus": {"LicenseName": "4647;701-teaminc.com", "LicenseKey": "***REMOVED***"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "SendGrid": {"APIKey": "***REMOVED***"}, "Emails": {"Support": [{"Email": "<EMAIL>", "GivenName": "<PERSON>", "Surname": "<PERSON><PERSON><PERSON><PERSON>"}]}}