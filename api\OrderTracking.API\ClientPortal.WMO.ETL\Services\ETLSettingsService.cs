using System;
using System.Threading.Tasks;
using ClientPortal.WMO.ETL.Models;
using Dapper;
using Dapper.Contrib.Extensions;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

namespace ClientPortal.WMO.ETL.Services
{
    public class ETLSettingsService : IETLSettingsService
    {
        private readonly string _connectionString;

        public ETLSettingsService(IConfiguration config)
        {
            _connectionString = config.GetConnectionString("Target");
        }

        public async Task SetCheckForDataLoss(bool checkForDataLoss)
        {
            await SetETLSetting(ETLSetting.CheckForDataLossName, checkForDataLoss);
        }

        public async Task<bool> GetCheckForDataLoss()
        {
            await using var targetConnection = new SqlConnection(_connectionString);
            await targetConnection.OpenAsync();

            var checkForDataLossSetting = await targetConnection.QueryFirstAsync<string>(
                "SELECT [Value] FROM [ETLSettings] WHERE [Name] = @name", new {name = ETLSetting.CheckForDataLossName});

            var checkForDataLoss = checkForDataLossSetting.ToLower() == "true";
            return checkForDataLoss;
        }

        public async Task SetDataLossThreshold(double dataLossThreshold)
        {
            await SetETLSetting(ETLSetting.DataLossThresholdName, dataLossThreshold);
        }

        public async Task CreateETLSettingsTableAsync()
        {
            await using var targetConnection = new SqlConnection(_connectionString);
            await targetConnection.ExecuteAsync($@"
CREATE TABLE [ETLSettings] (
	[Id]	INT IDENTITY(1, 1) PRIMARY KEY,
    [Name]  [NVARCHAR](50) NOT NULL UNIQUE,
    [Value] [NVARCHAR](MAX) NOT NULL
);

INSERT INTO [ETLSettings] (Name, Value)
VALUES 
    ('{ETLSetting.DataLossThresholdName}', '0.5'),
    ('{ETLSetting.CheckForDataLossName}', 'true')
");
        }

        public async Task<double> GetDataLossThreshold()
        {
            await using var targetConnection = new SqlConnection(_connectionString);
            await targetConnection.OpenAsync();

            var dataLossThresholdSetting =
                await targetConnection.QueryFirstAsync<string>(
                    "SELECT [Value] FROM [ETLSettings] WHERE [Name] = @name",
                    new {name = ETLSetting.DataLossThresholdName});

            var dataLossThreshold = Convert.ToDouble(dataLossThresholdSetting);

            return dataLossThreshold;
        }

        private async Task SetETLSetting(string name, object value)
        {
            await using var targetConnection = new SqlConnection(_connectionString);
            await targetConnection.OpenAsync();

            var setting = await targetConnection.QuerySingleAsync<ETLSetting>(
                "SELECT * FROM ETLSettings WHERE Name = @name", name);

            setting.Value = value.ToString();

            await targetConnection.UpdateAsync(setting);
        }
    }
}