import { DOCUMENT } from '@angular/common';
import { Component, Inject, OnInit, Renderer2 } from '@angular/core';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-esg',
  templateUrl: './esg.component.html',
  styleUrls: ['./esg.component.scss']
})
export class EsgComponent implements OnInit {

  googleAIConfig = environment.googleAI;

  constructor(private _renderer2: Renderer2, 
    @Inject(DOCUMENT) private _document: Document) { }

  ngOnInit(): void {
    // Only load Google AI if script URL is configured
    if (this.googleAIConfig.scriptUrl) {
      this.loadGoogleAIScript();
    }
  }

  private loadGoogleAIScript(): void {
    let script = this._renderer2.createElement('script');
    script.src = this.googleAIConfig.scriptUrl;
    this._renderer2.appendChild(this._document.body, script);
  }
}