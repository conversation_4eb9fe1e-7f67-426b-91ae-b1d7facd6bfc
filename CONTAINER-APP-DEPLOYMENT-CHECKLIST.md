# Azure Container App Deployment Checklist

Use this checklist to ensure your application works properly after deployment to Azure Container Apps.

## Pre-Deployment

### Backend API Configuration
- [x] Update CORS settings in `Startup.cs` to include Container App URL
- [ ] Verify all environment-specific configurations are set

### Frontend Configuration
- [x] Update environment.prod.ts to include Container App URL configuration
- [x] Configure API URL to point to the backend Container App
- [x] Set up authentication redirect URLs

## Azure Portal Configuration

### Azure AD App Registration
1. [ ] Go to Azure Portal → Azure Active Directory → App Registrations → Your App
2. [ ] Under "Authentication" tab:
   - [ ] Add `https://clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io` as a redirect URI
   - [ ] Ensure "Access tokens" and "ID tokens" are checked
3. [ ] Save changes

### Azure AD B2C (if applicable)
1. [ ] Go to Azure Portal → Azure AD B2C → App registrations
2. [ ] Select your application
3. [ ] Under "Authentication":
   - [ ] Add `https://clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io` as a redirect URI
4. [ ] Save changes

### Container App Configuration
1. [ ] Verify environment variables in Azure Container App
   - [ ] Check API connection strings
   - [ ] Check authentication settings
2. [ ] Ensure proper scaling rules are configured
3. [ ] Verify network configuration is correct

## Deployment

### Deploy Backend API
1. [ ] Build and push the backend Docker image
   ```
   docker build -t acrname.azurecr.io/testapi001:latest -f api/Dockerfiles/Dockerfile-cpa-backend .
   docker push acrname.azurecr.io/testapi001:latest
   ```
2. [ ] Deploy/update the backend Container App
   - [ ] Use Azure DevOps pipeline or Azure CLI

### Deploy Frontend
1. [ ] Build Angular app with production configuration
   ```
   cd wheres-my-order
   npm run build -- --configuration=production
   ```
2. [ ] Build and push frontend Docker image
   ```
   docker build -t acrname.azurecr.io/testfrontend001:latest .
   docker push acrname.azurecr.io/testfrontend001:latest
   ```
3. [ ] Deploy/update the frontend Container App
   - [ ] Use Azure DevOps pipeline or Azure CLI

## Post-Deployment Verification

### Backend API Verification
1. [ ] Verify API health endpoint
   - [ ] Navigate to `https://clientportal-api-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io/api/health`
2. [ ] Check API logs for errors
   - [ ] Use Azure Portal → Container App → Logs

### Frontend Verification
1. [ ] Verify application loads correctly
2. [ ] Test authentication flow
   - [ ] Login
   - [ ] Access protected resources
   - [ ] Logout
3. [ ] Test API integration
   - [ ] Verify data is fetched and displayed
   - [ ] Test form submissions

### Cross-Cutting Concerns
1. [ ] Verify Application Insights is capturing telemetry
2. [ ] Check for any CORS errors in browser console
3. [ ] Verify storage access works correctly
4. [ ] Test performance and responsiveness

## Troubleshooting Common Issues

### Authentication Issues
- If you see authentication failures, verify redirect URIs in both Azure AD/B2C and frontend configuration
- Check browser console for any CORS or token-related errors

### API Connection Issues
- Verify API URL is correctly specified in environment.prod.ts
- Check Network tab in browser DevTools for any failed API requests
- Ensure API is properly deployed and running

### Storage Access Issues
- Verify storage account keys/SAS tokens are correct
- Check permissions on storage containers
