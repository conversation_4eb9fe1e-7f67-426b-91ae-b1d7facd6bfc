# Azure Container App Environment Variables

## Backend Container App (testapi)

### Authentication and Security
ASPNETCORE_ENVIRONMENT=Production
ApplicationInsights__InstrumentationKey=<your-app-insights-instrumentation-key>
AzureAd__Instance=https://login.microsoftonline.com/
AzureAd__Domain=<your-azure-ad-domain>
AzureAd__TenantId=<your-azure-ad-tenant-id>
AzureAd__ClientId=<your-azure-ad-client-id>
AzureAd__ClientSecret=<your-azure-ad-client-secret>
AzureAd__CallbackPath=/signin-oidc

### Database Connections
ConnectionStrings__OrderTrackingContext=<your-sql-connection-string>
ConnectionStrings__ClientPortalContext=<your-sql-connection-string>
ConnectionStrings__StorageAccount=<your-azure-storage-connection-string>

### API Configuration
API__BaseUrl=https://clientportal-api-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io
API__Timeout=30
AllowedHosts=*
CorsOrigins=https://clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io

### Service Bus Configuration
ServiceBus__ConnectionString=<your-service-bus-connection-string>
ServiceBus__QueueName=<your-service-bus-queue-name>

### Logging
Logging__LogLevel__Default=Information
Logging__LogLevel__Microsoft=Warning
Logging__LogLevel__Microsoft.Hosting.Lifetime=Information

### Feature Flags
FeatureManagement__RemoteMonitoring=true
FeatureManagement__EquipmentDrillDown=true
FeatureManagement__AnomalyDetection=true

## Frontend Container App (testfrontend)

### API Endpoints
API_URL=https://clientportal-api-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io
AUTH_URL=https://clientportal-api-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io/auth
NODE_ENV=production

### Authentication
CLIENT_ID=<your-azure-ad-client-id>
TENANT_ID=<your-azure-ad-tenant-id>
REDIRECT_URI=https://clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io/auth-callback

### Application Settings
APP_VERSION=1.0.0
DEFAULT_TIMEOUT=30000

## Remote Monitoring Container App (if applicable)

### Service Configuration
ASPNETCORE_ENVIRONMENT=Production
ConnectionStrings__OrderTrackingContext=<your-sql-connection-string>
ConnectionStrings__StorageAccount=<your-azure-storage-connection-string>
ServiceBus__ConnectionString=<your-service-bus-connection-string>
ServiceBus__TopicName=<your-service-bus-topic-name>
JobConfiguration__Schedule=0 */15 * * * *
JobConfiguration__TimeoutInMinutes=10

## Notes on Adding Environment Variables

You can add these environment variables to your Azure Container Apps using:

1. **Azure Portal**: Navigate to the Container App > Configuration > Environment variables
2. **Azure CLI**:
   ```
   az containerapp update \
     --name <container-app-name> \
     --resource-group <resource-group-name> \
     --set-env-vars "KEY1=VALUE1" "KEY2=VALUE2"
   ```
3. **Bicep/ARM Templates**: Include environment variables in your infrastructure as code
4. **Azure DevOps Pipeline**:
   ```yaml
   - task: AzureContainerApp@1
     inputs:
       azureSubscription: $(azureSubscription)
       containerAppName: $(containerAppName)
       resourceGroup: $(resourceGroup)
       environmentVariables: |
         KEY1=$(VALUE1)
         KEY2=$(VALUE2)
   ```

Replace placeholder values (`<your-...>`) with actual values from your environment.
