export const environment = {
    azureAd: {
        clientId: '65cae7df-c271-4ffe-aa09-181195221ea2' ,
        authority: 'https://login.microsoftonline.com/3cfc49f9-956e-4e4e-a1b6-e03368c2e448',
        redirectUri: '___RedirectUri___',
        postLogoutRedirectUri: '___PostLogoutRedirectUri___'
    },
    azureStorage: {
        accountName: '___AzureStorageAccount___',
        containerName: 'app-data',
        sasToken: '___AzureStorageSasToken___'
    },
    production: true,
    api: {
        url: '___ApiUrl___'
    },
    hubs: {
        edr: '___SignalRUrl___/edr'
    },
    msal: {
        clientID: '2e305521-1e55-42bf-a6ca-aa1ff0d7bff3',
        authority:
            'https://teamincb2c.b2clogin.com/teamincb2c.onmicrosoft.com/B2C_1_signupsignin',
        forgotPasswordAuthority:
            'https://teamincb2c.b2clogin.com/teamincb2c.onmicrosoft.com/B2C_1_forgotpassword',
        redirectUri: '___RedirectUri___',
        postLogoutRedirectUri: '___PostLogoutRedirectUri___'
    },
    appInsights: {
        instrumentationKey: '95205a62-1384-45d2-bc18-d68987f479af'
    },
    credo: {
        fileShare: {
            account: 'vmdiagdisklwuvksj4d7ng4',
            sas: '?sv=2020-08-04&ss=f&srt=sco&sp=rl&se=2023-01-01T05:56:45Z&st=2021-12-09T21:56:45Z&spr=https&sig=bJwHEw6XaR96K%2FUepQpjud3ajBH8To899GZeIvk3cNo%3D',
            shareName: 'credosoft'
        }
    },
    apm: {
        photoContainer:
            'https://aznascpiadevsa.blob.core.windows.net/flutter-dev/'
    },
    googleAI: {
        scriptUrl: 'https://cloud.google.com/ai/gen-app-builder/client',
        configId: 'bdf8bb2b-0cbe-4733-9871-61be0618212e',
        triggerId: 'searchWidgetTrigger'
    }
};

if (location.hostname === 'localhost') {
    environment.api.url = 'https://localhost:5001/api';
    environment.msal.redirectUri = 'http://localhost:4200';
    environment.msal.postLogoutRedirectUri = 'http://localhost:4200';
    environment.credo = {
        fileShare: {
            account: 'vmdiagdisk5raqsvzbbo5e6',
            sas: '?sv=2020-08-04&ss=f&srt=sco&sp=rl&se=2023-01-01T05:52:00Z&st=2021-12-09T21:52:00Z&spr=https&sig=EolL8M3CiXETChPrAGE%2BHrg1B0b3oWs9XOSBNZRokfw%3D',
            shareName: 'credosoft'
        }
    };
} else if (location.hostname === 'digitaldev.teaminc.com') {
    environment.api.url =
        'https://run-clientportal-api-dev-usc1-2conmidenq-uc.a.run.app/api';
    environment.msal.redirectUri = 'https://digitaldev.teaminc.com';
    environment.msal.postLogoutRedirectUri = 'https://digitaldev.teaminc.com';
    environment.credo = {
        fileShare: {
            account: 'vmdiagdisk5raqsvzbbo5e6',
            sas: '?sv=2020-08-04&ss=f&srt=sco&sp=rl&se=2023-01-01T05:52:00Z&st=2021-12-09T21:52:00Z&spr=https&sig=EolL8M3CiXETChPrAGE%2BHrg1B0b3oWs9XOSBNZRokfw%3D',
            shareName: 'credosoft'
        }
    };
} else if (location.hostname === 'digitaltest.teaminc.com') {
    environment.api.url =
        'https://run-clientportal-api-stg-usc1-2atiipqgyq-uc.a.run.app/api';
    environment.msal.redirectUri = 'https://digitaltest.teaminc.com';
    environment.msal.postLogoutRedirectUri = 'https://digitaltest.teaminc.com';
    environment.credo = {
        fileShare: {
            account: 'vmdiagdisk5raqsvzbbo5e6',
            sas: '?sv=2020-08-04&ss=f&srt=sco&sp=rl&se=2023-01-01T05:52:00Z&st=2021-12-09T21:52:00Z&spr=https&sig=EolL8M3CiXETChPrAGE%2BHrg1B0b3oWs9XOSBNZRokfw%3D',
            shareName: 'credosoft'
        }
    };
} else if (location.hostname === 'digitalstaging.teaminc.com') {
    environment.api.url =
        'https://run-clientportal-api-stg-usc1-2atiipqgyq-uc.a.run.app/api';
    environment.msal.redirectUri = 'https://digitalstaging.teaminc.com';
    environment.msal.postLogoutRedirectUri =
        'https://digitalstaging.teaminc.com';
    environment.credo = {
        fileShare: {
            account: 'vmdiagdisk5raqsvzbbo5e6',
            sas: '?sv=2020-08-04&ss=f&srt=sco&sp=rl&se=2023-01-01T05:52:00Z&st=2021-12-09T21:52:00Z&spr=https&sig=EolL8M3CiXETChPrAGE%2BHrg1B0b3oWs9XOSBNZRokfw%3D',
            shareName: 'credosoft'
        }
    };
} else if (location.hostname === 'digital.teaminc.com') {
    environment.api.url =
        'https://ca-cpa-backend-prod.azurecontainerapps.io/api';
    environment.msal.redirectUri = 'https://digital.teaminc.com';
    environment.msal.postLogoutRedirectUri = 'https://digital.teaminc.com';

    // Migrated from Firebase to Azure services
    // environment.firebase = {
    //     apiKey: 'AIzaSyBi-1HcJVl-UpDtZvKeG65wdDbdffwYEQE',
    //     authDomain: 'apm-prod-da61a.firebaseapp.com',
    //     projectId: 'apm-prod-da61a',
    //     storageBucket: 'apm-prod-da61a.appspot.com',
    //     messagingSenderId: '************',
    //     appId: '1:************:web:79b62bada991637aa9e96d',
    //     measurementId: 'G-W2GS8C0WMY',
    //     databaseURL: undefined,
    //     locationId: undefined
    // };
} else if (location.hostname === 'oneinsight-luqdfznyta-uc.a.run.app') {
    environment.api.url = 'https://client-portal-api-dev.azurewebsites.net/api';
    environment.msal.redirectUri = 'https://oneinsight-luqdfznyta-uc.a.run.app';
    environment.msal.postLogoutRedirectUri =
        'https://oneinsight-luqdfznyta-uc.a.run.app';
    environment.credo = {
        fileShare: {
            account: 'vmdiagdisk5raqsvzbbo5e6',
            sas: '?sv=2020-08-04&ss=f&srt=sco&sp=rl&se=2023-01-01T05:52:00Z&st=2021-12-09T21:52:00Z&spr=https&sig=EolL8M3CiXETChPrAGE%2BHrg1B0b3oWs9XOSBNZRokfw%3D',
            shareName: 'credosoft'
        }
    };
} else if (
    location.hostname ===
    'run-clientportal-frontend-dev-usc1-2conmidenq-uc.a.run.app'
) {
    environment.api.url =
        'https://run-clientportal-api-dev-usc1-2conmidenq-uc.a.run.app/api';
    environment.msal.redirectUri =
        'https://run-clientportal-frontend-dev-usc1-2conmidenq-uc.a.run.app';
    environment.msal.postLogoutRedirectUri =
        'https://run-clientportal-frontend-dev-usc1-2conmidenq-uc.a.run.app';
    environment.credo = {
        fileShare: {
            account: 'vmdiagdisk5raqsvzbbo5e6',
            sas: '?sv=2020-08-04&ss=f&srt=sco&sp=rl&se=2023-01-01T05:52:00Z&st=2021-12-09T21:52:00Z&spr=https&sig=EolL8M3CiXETChPrAGE%2BHrg1B0b3oWs9XOSBNZRokfw%3D',
            shareName: 'credosoft'
        }
    };
} else if (
    location.hostname ===
    'run-clientportal-frontend-stg-usc1-2atiipqgyq-uc.a.run.app'
) {
    environment.api.url =
        'https://run-clientportal-api-stg-usc1-2atiipqgyq-uc.a.run.app/api';
    environment.msal.redirectUri =
        'https://run-clientportal-frontend-stg-usc1-2atiipqgyq-uc.a.run.app';
    environment.msal.postLogoutRedirectUri =
        'https://run-clientportal-frontend-stg-usc1-2atiipqgyq-uc.a.run.app';
    environment.credo = {
        fileShare: {
            account: 'vmdiagdisk5raqsvzbbo5e6',
            sas: '?sv=2020-08-04&ss=f&srt=sco&sp=rl&se=2023-01-01T05:52:00Z&st=2021-12-09T21:52:00Z&spr=https&sig=EolL8M3CiXETChPrAGE%2BHrg1B0b3oWs9XOSBNZRokfw%3D',
            shareName: 'credosoft'
        }
    };
} else if (
    location.hostname ===
    'run-clientportal-frontend-prod-usc1-yonwy2nc7q-uc.a.run.app'
) {
    environment.api.url =
        'https://run-clientportal-api-prod-usc1-yonwy2nc7q-uc.a.run.app/api';
    environment.msal.redirectUri =
        'https://run-clientportal-frontend-prod-usc1-yonwy2nc7q-uc.a.run.app';
    environment.msal.postLogoutRedirectUri =
        'https://run-clientportal-frontend-prod-usc1-yonwy2nc7q-uc.a.run.app';
    environment.credo = {
        fileShare: {
            account: 'vmdiagdisk5raqsvzbbo5e6',
            sas: '?sv=2020-08-04&ss=f&srt=sco&sp=rl&se=2023-01-01T05:52:00Z&st=2021-12-09T21:52:00Z&spr=https&sig=EolL8M3CiXETChPrAGE%2BHrg1B0b3oWs9XOSBNZRokfw%3D',
            shareName: 'credosoft'
        }
    };
    // Migrated from Firebase to Azure services
    // environment.firebase = {
    //     apiKey: 'AIzaSyBi-1HcJVl-UpDtZvKeG65wdDbdffwYEQE',
    //     authDomain: 'apm-prod-da61a.firebaseapp.com',
    //     projectId: 'apm-prod-da61a',
    //     storageBucket: 'apm-prod-da61a.appspot.com',
    //     messagingSenderId: '************',
    //     appId: '1:************:web:79b62bada991637aa9e96d',
    //     measurementId: 'G-W2GS8C0WMY',
    //     databaseURL: undefined,
    //     locationId: undefined
    // };
}
 else if (
    location.hostname === 'clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io'
) {
 
    // Specific configuration for the new Azure Container App
    environment.api.url = 'https://clientportal-api-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io/api';
    environment.msal.redirectUri = 'https://clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io';
    environment.msal.postLogoutRedirectUri = 'https://clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io';
    environment.azureAd.redirectUri = 'https://clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io';
    environment.azureAd.postLogoutRedirectUri = 'https://clientportal-frontend-dev-ussc1.salmonrock-7edcc7b9.southcentralus.azurecontainerapps.io';
    
    environment.credo = {
        fileShare: {
            account: 'stakrakendev001', // You may need to update this to your actual storage account
            sas: '?sv=2020-08-04&ss=f&srt=sco&sp=rl&se=2023-01-01T05:52:00Z&st=2021-12-09T21:52:00Z&spr=https&sig=EolL8M3CiXETChPrAGE%2BHrg1B0b3oWs9XOSBNZRokfw%3D',
            shareName: 'credosoft'
        }
    };
}
