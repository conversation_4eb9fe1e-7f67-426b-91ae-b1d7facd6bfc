# Backend Logging and Application Insights Implementation

This document outlines the comprehensive logging solution implemented for the backend API using Azure Application Insights.

## Overview

The logging implementation includes:
- **Global Exception Handling** - Catches and logs all unhandled exceptions
- **Structured Logging** - Consistent logging format with correlation IDs
- **Performance Monitoring** - Tracks API response times and performance metrics
- **User Activity Tracking** - Logs user actions and resource access
- **Health Monitoring** - Health check endpoints with logging

## Components

### 1. Global Exception Middleware (`GlobalExceptionMiddleware.cs`)
- Catches all unhandled exceptions across the application
- Logs exceptions to Application Insights with contextual information
- Provides consistent error responses
- Includes request path, method, user information, and IP address

### 2. API Logging Action Filter (`ApiLoggingActionFilter.cs`)
- Automatically logs all API requests and responses
- Tracks execution time for performance monitoring
- Records success/failure status
- Includes user context and request metadata

### 3. Application Logging Service (`ApplicationLoggingService.cs`)
- Centralized logging service for structured logging
- Integrates with Application Insights telemetry
- Provides methods for different log levels
- Tracks custom events and user actions

### 4. Health Check Controller (`HealthController.cs`)
- Basic health check endpoint: `/api/health`
- Detailed health check endpoint: `/api/health/detailed`
- Includes logging for monitoring health check requests

## Configuration

### Application Insights Settings (`appsettings.json`)
```json
{
  "ApplicationInsights": {
    "InstrumentationKey": "95205a62-1384-45d2-bc18-d68987f479af",
    "ConnectionString": "InstrumentationKey=95205a62-1384-45d2-bc18-d68987f479af"
  },
  "Logging": {
    "ApplicationInsights": {
      "LogLevel": {
        "Default": "Information",
        "Microsoft": "Warning",
        "OrderTracking.API": "Information"
      }
    }
  }
}
```

### Startup Configuration (`Startup.cs`)
- Registers Application Insights telemetry
- Adds global exception middleware
- Registers logging services and action filters
- Configures structured logging levels

## Usage Examples

### In Controllers
```csharp
public class ExampleController : ControllerBase
{
    private readonly IApplicationLoggingService _appLogger;
    
    public ExampleController(IApplicationLoggingService appLogger)
    {
        _appLogger = appLogger;
    }
    
    [HttpGet]
    public async Task<IActionResult> GetData()
    {
        try
        {
            var user = User?.Identity?.Name ?? "Anonymous";
            _appLogger.LogInformation("Getting data", new { RequestedBy = user });
            
            var data = await GetSomeData();
            
            _appLogger.TrackUserAction(user, "GetData", "DataResource", true, 
                new { RecordCount = data.Count });
                
            return Ok(data);
        }
        catch (Exception ex)
        {
            _appLogger.LogError(ex, "Failed to get data");
            throw; // Will be caught by GlobalExceptionMiddleware
        }
    }
}
```

### Custom Event Tracking
```csharp
_appLogger.TrackEvent("CustomBusinessEvent", 
    new Dictionary<string, string> 
    {
        { "UserId", userId },
        { "Action", "SpecificAction" },
        { "Resource", "ResourceName" }
    },
    new Dictionary<string, double>
    {
        { "ProcessingTimeMs", processingTime },
        { "RecordCount", recordCount }
    });
```

### Dependency Tracking
```csharp
var startTime = DateTime.UtcNow;
var stopwatch = Stopwatch.StartNew();

try
{
    // Call external service
    var result = await externalService.CallAsync();
    stopwatch.Stop();
    
    _appLogger.TrackDependency("HTTP", "ExternalAPI", "GetData", 
        startTime, stopwatch.Elapsed, true);
}
catch (Exception ex)
{
    stopwatch.Stop();
    _appLogger.TrackDependency("HTTP", "ExternalAPI", "GetData", 
        startTime, stopwatch.Elapsed, false);
    throw;
}
```

## Monitoring and Alerts

### Key Metrics Tracked
- **Request Volume** - Number of API calls per endpoint
- **Response Times** - Average and percentile response times
- **Error Rates** - Failed requests and exception counts
- **User Activity** - User actions and resource access patterns
- **Health Status** - Application health and availability

### Recommended Application Insights Queries

#### Error Rate by Endpoint
```kusto
requests
| where timestamp > ago(1h)
| summarize ErrorRate = (countif(success == false) * 100.0) / count() by name
| order by ErrorRate desc
```

#### Top Slow Requests
```kusto
requests
| where timestamp > ago(1h)
| top 10 by duration desc
| project timestamp, name, duration, url, resultCode
```

#### User Activity Summary
```kusto
customEvents
| where name == "UserAction"
| where timestamp > ago(24h)
| summarize ActionCount = count() by tostring(customDimensions.UserId), tostring(customDimensions.Action)
| order by ActionCount desc
```

#### Exception Analysis
```kusto
exceptions
| where timestamp > ago(24h)
| summarize ExceptionCount = count() by type, outerMessage
| order by ExceptionCount desc
```

## Health Check Endpoints

### Basic Health Check
- **URL**: `GET /api/health`
- **Response**: Basic health status with timestamp
- **Use**: Load balancer health checks

### Detailed Health Check
- **URL**: `GET /api/health/detailed`
- **Response**: Detailed health status with dependency checks
- **Use**: Monitoring dashboards and detailed diagnostics

## Performance Considerations

1. **Async Logging** - Application Insights uses async telemetry sending
2. **Sampling** - Configure sampling rates for high-volume applications
3. **Correlation** - All logs include correlation IDs for request tracing
4. **Structured Data** - Use structured logging for better querying capabilities

## Security Considerations

1. **PII Protection** - Avoid logging sensitive user data
2. **Authentication Context** - Log user context without exposing credentials
3. **IP Logging** - Log client IP addresses for security monitoring
4. **Data Retention** - Configure appropriate data retention policies

## Troubleshooting

### Common Issues
1. **Missing Telemetry** - Check Application Insights connection string
2. **High Latency** - Review telemetry sampling configuration
3. **Missing Context** - Ensure middleware is registered in correct order
4. **Authentication Issues** - Verify Azure AD B2C configuration

### Debug Configuration
For development environments, you can increase logging verbosity:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "OrderTracking.API": "Debug"
    }
  }
}
```

## Next Steps

1. **Custom Dashboards** - Create Application Insights dashboards for key metrics
2. **Alerting Rules** - Set up alerts for error rates and performance thresholds
3. **Integration Tests** - Add logging validation to integration tests
4. **Performance Baselines** - Establish performance baselines for monitoring
