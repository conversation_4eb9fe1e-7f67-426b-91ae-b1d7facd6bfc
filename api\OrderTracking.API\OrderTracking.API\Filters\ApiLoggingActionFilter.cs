using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace OrderTracking.API.Filters
{
    /// <summary>
    /// Action filter that logs API requests and responses using Microsoft.Extensions.Logging
    /// </summary>
    public class ApiLoggingActionFilter : ActionFilterAttribute
    {
        private readonly ILogger<ApiLoggingActionFilter> _logger;
        private readonly TelemetryClient _telemetryClient;
        private Stopwatch _stopwatch;

        public ApiLoggingActionFilter(ILogger<ApiLoggingActionFilter> logger, TelemetryClient telemetryClient)
        {
            _logger = logger;
            _telemetryClient = telemetryClient;
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            _stopwatch = Stopwatch.StartNew();

            var controllerName = context.Controller.GetType().Name;
            var actionName = context.ActionDescriptor.DisplayName;
            var userId = context.HttpContext.User?.Identity?.Name ?? "Anonymous";
            var requestPath = context.HttpContext.Request.Path.Value;
            var requestMethod = context.HttpContext.Request.Method;
            var userAgent = context.HttpContext.Request.Headers["User-Agent"].ToString();
            var remoteIp = context.HttpContext.Connection.RemoteIpAddress?.ToString();
            var traceId = context.HttpContext.TraceIdentifier;

            // Use structured logging with Microsoft.Extensions.Logging
            using var scope = _logger.BeginScope(new Dictionary<string, object>
            {
                ["Controller"] = controllerName,
                ["Action"] = actionName,
                ["UserId"] = userId,
                ["RequestPath"] = requestPath,
                ["RequestMethod"] = requestMethod,
                ["TraceId"] = traceId,
                ["RemoteIP"] = remoteIp
            });

            _logger.LogInformation(
                "API Request Started: {Controller}.{Action} by {UserId} - {RequestMethod} {RequestPath} from {RemoteIP}",
                controllerName, actionName, userId, requestMethod, requestPath, remoteIp);

            // Track custom event in Application Insights
            var properties = new Dictionary<string, string>
            {
                { "Controller", controllerName },
                { "Action", actionName },
                { "RequestPath", requestPath ?? "" },
                { "RequestMethod", requestMethod },
                { "UserId", userId },
                { "UserAgent", userAgent },
                { "RemoteIP", remoteIp ?? "" },
                { "TraceId", traceId },
                { "ArgumentCount", context.ActionArguments.Count.ToString() }
            };

            _telemetryClient.TrackEvent("API_Request_Started", properties);

            base.OnActionExecuting(context);
        }

        public override void OnActionExecuted(ActionExecutedContext context)
        {
            _stopwatch?.Stop();
            var executionTime = _stopwatch?.ElapsedMilliseconds ?? 0;

            var controllerName = context.Controller.GetType().Name;
            var actionName = context.ActionDescriptor.DisplayName;
            var userId = context.HttpContext.User?.Identity?.Name ?? "Anonymous";
            var requestPath = context.HttpContext.Request.Path.Value;
            var requestMethod = context.HttpContext.Request.Method;
            var statusCode = context.HttpContext.Response.StatusCode;
            var userAgent = context.HttpContext.Request.Headers["User-Agent"].ToString();
            var remoteIp = context.HttpContext.Connection.RemoteIpAddress?.ToString();
            var traceId = context.HttpContext.TraceIdentifier;
            var isSuccess = context.Exception == null && statusCode < 400;

            // Use structured logging with Microsoft.Extensions.Logging
            using var scope = _logger.BeginScope(new Dictionary<string, object>
            {
                ["Controller"] = controllerName,
                ["Action"] = actionName,
                ["UserId"] = userId,
                ["RequestPath"] = requestPath,
                ["RequestMethod"] = requestMethod,
                ["StatusCode"] = statusCode,
                ["ExecutionTimeMs"] = executionTime,
                ["TraceId"] = traceId,
                ["RemoteIP"] = remoteIp,
                ["Success"] = isSuccess
            });

            // Log based on success/failure
            if (context.Exception != null)
            {
                _logger.LogError(context.Exception,
                    "API Request Failed: {Controller}.{Action} by {UserId} - {RequestMethod} {RequestPath} returned {StatusCode} in {ExecutionTimeMs}ms",
                    controllerName, actionName, userId, requestMethod, requestPath, statusCode, executionTime);
            }
            else if (!isSuccess)
            {
                _logger.LogWarning(
                    "API Request Completed with Error: {Controller}.{Action} by {UserId} - {RequestMethod} {RequestPath} returned {StatusCode} in {ExecutionTimeMs}ms",
                    controllerName, actionName, userId, requestMethod, requestPath, statusCode, executionTime);
            }
            else
            {
                _logger.LogInformation(
                    "API Request Completed: {Controller}.{Action} by {UserId} - {RequestMethod} {RequestPath} returned {StatusCode} in {ExecutionTimeMs}ms",
                    controllerName, actionName, userId, requestMethod, requestPath, statusCode, executionTime);
            }

            // Track custom event in Application Insights
            var properties = new Dictionary<string, string>
            {
                { "Controller", controllerName },
                { "Action", actionName },
                { "RequestPath", requestPath ?? "" },
                { "RequestMethod", requestMethod },
                { "UserId", userId },
                { "StatusCode", statusCode.ToString() },
                { "Success", isSuccess.ToString() },
                { "UserAgent", userAgent },
                { "RemoteIP", remoteIp ?? "" },
                { "TraceId", traceId }
            };

            var metrics = new Dictionary<string, double>
            {
                { "ExecutionTimeMs", executionTime }
            };

            _telemetryClient.TrackEvent("API_Request_Completed", properties, metrics);

            // Track execution time as dependency for performance monitoring
            _telemetryClient.TrackDependency("API", $"{controllerName}.{actionName}", 
                requestPath, DateTime.UtcNow.AddMilliseconds(-executionTime), 
                TimeSpan.FromMilliseconds(executionTime), isSuccess);

            base.OnActionExecuted(context);
        }
    }
}
