{"ConnectionStrings": {"RemoteMonitoring": "***REMOVED***"}, "AnteaDb": {"ConnectionString": ""}, "AzureAdB2C": {"Instance": "https://teamincb2c.b2clogin.com/", "ClientId": "***REMOVED***", "Domain": "teamincb2c.onmicrosoft.com", "SignUpSignInPolicyId": "B2C_1_signupsignin"}, "AzureAd": {"AuthenticationMode": "ServicePrincipal", "AuthorityUri": "https://login.microsoftonline.com/organizations/", "ClientId": "0a66b99f-3478-4b56-91c4-d674b0225f9d", "TenantId": "3cfc49f9-956e-4e4e-a1b6-e03368c2e448", "Scope": ["https://analysis.windows.net/powerbi/api/.default"], "PbiUsername": "", "PbiPassword": "", "ClientSecret": "***REMOVED***"}, "BlobStorage": {"BlobEndpoint": "https://tdclientportalfiles.blob.core.windows.net/", "AccountName": "tdclientportalfiles", "APMContainer": "apmdev", "APMKey": "***REMOVED***", "APMReportingContainer": "apmdev-reporting", "APMStorageAccountName": "stakrakendev001", "APMBlobContainerName": "apm-dev", "APMWOStorageAccountName": "stakrakendev001", "APMWOBlobContainerName": "apm-workorders-dev", "KeyVaultName": "kv-kraken-dev-001"}, "Connections": {"Database": "oneinsight-auth", "UserProfiles": "user-profiles", "Roles": "roles", "EquipmentRequests": "equipment-requests", "FlangeCalculations": "flange-calculations", "Notifications": "notifications", "ReleaseNotes": "release-notes", "AuthHistory": "auth-history", "KeyVaultName": "kv-kraken-dev-001", "ResourceGroupName": "rg-digital-poc", "SubscriptionId": "", "AnteaAttachmentsBlobContainer": "anteaattachmentspoc", "AnteaSubmissionsBlobContainer": "anteasubmissionspoc", "AnteaStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=anteasubmissionspoc;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"}, "APM": {"Environment": "testing", "Firebase": "", "CmsSettings": {"CMSRootObjectName": "Inspection", "CMSBaseInspectionsContainerName": "cms-base-inspections", "CMSClientInspectionsContainerName": "cms-client-inspections", "CMSClientInspectionsSitesSubcontainerName": "sites", "CMSClientInspectionsSitesInspectionsSubcontainerName": "inspections", "CMSClientInspectionsAllSitesSubcontainerName": "all-sites-inspections", "CMSDistrictInspectionsContainerName": "cms-district-inspections", "CMSDistrictInspectionsSitesSubcontainerName": "sites", "CMSDistrictInspectionsSitesInspectionsSubcontainerName": "inspections", "CMSDistrictInspectionsAllSitesSubcontainerName": "all-sites-inspections", "CMSBaseInspectionsFirestoreCollectionName": "cms-base-inspections", "CMSClientInspectionsFirestoreCollectionName": "cms-client-inspections", "CMSClientInspectionsSitesFirestoreSubcollectionName": "sites", "CMSClientInspectionsSitesInspectionsFirestoreSubcollectionName": "inspections", "CMSClientInspectionsAllSitesFirestoreSubcollectionName": "all-sites-inspections", "CMSDistrictInspectionsFirestoreCollectionName": "cms-district-inspections", "CMSDistrictInspectionsSitesFirestoreSubcollectionName": "sites", "CMSDistrictInspectionsSitesInspectionsFirestoreSubcollectionName": "inspections", "CMSDistrictInspectionsAllSitesFirestoreSubcollectionName": "all-sites-inspections"}}, "PowerBI": {"WorkspaceId": "f41e97a3-6f9f-43a5-993c-0caeeacbdc73", "ReportId": "05a80050-2764-46ce-8156-f6fb06767c72"}, "ZDapperPlus": {"LicenseName": "4647;701-teaminc.com", "LicenseKey": "***REMOVED***"}, "ApplicationInsights": {"InstrumentationKey": "fc63cc35-c684-42fb-9442-055e806fbaf6", "ConnectionString": "InstrumentationKey=fc63cc35-c684-42fb-9442-055e806fbaf6"}, "SendGrid": {"APIKey": "***REMOVED***"}, "Swagger": {"ClientId": "***REMOVED***", "ClientSecret": "***REMOVED***"}, "FSMConfig": {"UserName": "***REMOVED***", "Password": "***REMOVED***", "Url": "***REMOVED***", "UpdateFlowUrl": "***REMOVED***"}, "Logging": {"ApplicationInsights": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore": "Warning", "OrderTracking.API": "Information"}}, "LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore": "Warning", "OrderTracking.API": "Information", "OrderTracking.API.Middleware.GlobalExceptionMiddleware": "Error", "OrderTracking.API.Filters.ApiLoggingActionFilter": "Information"}, "Console": {"IncludeScopes": true, "LogLevel": {"Default": "Information", "Microsoft": "Warning"}}}, "DeploymentEnvironment": "Local", "AllowedHosts": "*", "Clients": {"ClientPortal": "http://localhost:4200"}}