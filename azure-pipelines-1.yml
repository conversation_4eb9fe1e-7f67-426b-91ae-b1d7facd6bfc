# Azure DevOps Build Pipeline for Kraken Project
# Builds both Backend API and Frontend Application
 
trigger:
  branches:
    include:
      - main
      - migration/LocalTest
  paths:
    include:
      - api/*
      - wheres-my-order/*
 
 
pool:
  vmImage: 'ubuntu-latest'
 
variables:
  buildConfiguration: 'Release'
  dotNetVersion: '6.0.x'
  nodeVersion: '18.x'
 
stages:
- stage: Backend
  displayName: 'Build Backend API'
  jobs:
  - job: BuildBackend
    displayName: 'Build .NET API'
    steps:
    - task: UseDotNet@2
      displayName: 'Use .NET SDK $(dotNetVersion)'
      inputs:
        packageType: 'sdk'
        version: '$(dotNetVersion)'
 
    - task: NuGetAuthenticate@1
      displayName: 'Authenticate with NuGet Feeds'
 
    # Restore and build in dependency order
    - task: DotNetCoreCLI@2
      displayName: 'Restore ClientPortal.Shared'
      inputs:
        command: 'restore'
        projects: 'api/OrderTracking.API/OrderTracking.sln'
        feedsToUse: 'config'
        nugetConfigPath: 'api/OrderTracking.API/NuGet.Config'
        arguments: '--no-cache --verbosity detailed'
    
    - task: DotNetCoreCLI@2
      displayName: 'Build OrderTracking.API'
      inputs:
        command: 'build'
        projects: 'api/OrderTracking.API/OrderTracking.sln'
        arguments: '--configuration $(buildConfiguration) --no-restore'

     # Publish applications
    - task: DotNetCoreCLI@2
      displayName: 'Publish OrderTracking.API'
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: 'api/OrderTracking.API/OrderTracking.API/OrderTracking.API.csproj'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/api/OrderTracking.API'
        zipAfterPublish: false

   
    

- stage: Deploy
  displayName: 'Deploy to Dev'
  jobs:
  - job: Deploy

    steps:
    - task: Docker@2
      displayName: 'Build & Push Docker Image'
      inputs:
        command: 'buildAndPush'
        containerRegistry: 'Dev_ACR'  # Replace with actual ACR service connection name
        repository: 'kraken-backend'
        dockerfile: 'api/Dockerfiles/Dockerfile-cpa-backend'
        buildContext: '.'
        tags: |
          $(Build.BuildId)
          latest
        arguments: |
          --build-arg FEED_ACCESSTOKEN=1bfO0Jfb7TxGJ1QiUPOzOInlSmp8bvLa2u1EHgHYFn0friT821EOJQQJ99BGACAAAAANtmU4AAASAZDO3gLT
    - task: Bash@3
      inputs:
          targetType: 'inline'
          script: |
            # Write your commands here
            
            curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
            az --version
    - task: AzureCLI@2
      inputs:
          azureSubscription: 'TEAM-CLAND-01 (8de261da-54e3-4334-9076-284334fc2305)'
          scriptType: 'bash'
          scriptLocation: 'inlineScript'
          inlineScript: 'az containerapp update --name clientportal-api-dev-ussc1 --resource-group rg-digital-dev  --image acrdevdigitalapps001.azurecr.io/kraken-backend:$(Build.BuildId)'
      displayName: 'Build Backend Application'

- stage: Deploy_UAT
  displayName: 'Deploy to UAT'
  dependsOn: Deploy
  condition: succeeded()
  jobs:
  - job: DeployUAT
    displayName: 'Deploy to UAT Container App'
    steps:
    - task: Docker@2
      displayName: 'Build & Push Docker Image'
      inputs:
        command: 'buildAndPush'
        containerRegistry: 'UAT_ACR'  # Replace with actual ACR service connection name
        repository: 'kraken-backend'
        dockerfile: 'api/Dockerfiles/Dockerfile-cpa-backend'
        buildContext: '.'
        tags: |
          $(Build.BuildId)
          latest
        arguments: |
          --build-arg FEED_ACCESSTOKEN=1bfO0Jfb7TxGJ1QiUPOzOInlSmp8bvLa2u1EHgHYFn0friT821EOJQQJ99BGACAAAAANtmU4AAASAZDO3gLT
    - task: Bash@3
      inputs:
          targetType: 'inline'
          script: |
            # Write your commands here
            
            curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
            az --version
    - task: AzureCLI@2
      inputs:
            azureSubscription: 'TEAM-CLAND-01 (8de261da-54e3-4334-9076-284334fc2305)'
            scriptType: 'bash'
            scriptLocation: 'inlineScript'
            inlineScript: 'az containerapp update --name clientportal-api-uat-ussc1 --resource-group rg-digital-uat  --image acruatdigitalapps001.azurecr.io/kraken-backend:$(Build.BuildId)'
      displayName: 'Build Backend Application'

- stage: Deploy_Prod
  displayName: 'Deploy to Prod'
  dependsOn: Deploy_UAT
  condition: succeeded()
  jobs:
  - job: DeployProd
    displayName: 'Deploy to Prod Container App'
    steps:
    - task: Docker@2
      displayName: 'Build & Push Docker Image'
      inputs:
        command: 'buildAndPush'
        containerRegistry: 'PROD_ACR'  # Replace with actual ACR service connection name
        repository: 'kraken-backend'
        dockerfile: 'api/Dockerfiles/Dockerfile-cpa-backend'
        buildContext: '.'
        tags: |
          $(Build.BuildId)
          latest
        arguments: |
          --build-arg FEED_ACCESSTOKEN=1bfO0Jfb7TxGJ1QiUPOzOInlSmp8bvLa2u1EHgHYFn0friT821EOJQQJ99BGACAAAAANtmU4AAASAZDO3gLT
    - task: Bash@3
      inputs:
          targetType: 'inline'
          script: |
            # Write your commands here
            
            curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
            az --version
    # - task: AzureCLI@2
    #   inputs:
    #         azureSubscription: 'TEAM-CLAND-01 (8de261da-54e3-4334-9076-284334fc2305)'
    #         scriptType: 'bash'
    #         scriptLocation: 'inlineScript'
    #         inlineScript: 'az containerapp update --name clientportal-api-uat-ussc1 --resource-group rg-digital-uat  --image acruatdigitalapps001.azurecr.io/kraken-backend:$(Build.BuildId)'
    #   displayName: 'Build Backend Application'

########################################################################################################################################################

# - stage:  backend2
#   displayName: 'Build Backend API'
#   jobs:
#   - job: BuildBackend
#     displayName: 'Build .NET API'
#     steps:
#     - task: UseDotNet@2
#       inputs:
#         packageType: 'sdk'
#         version: '$(dotNetVersion)'
 
    # - task: NuGetAuthenticate@1
 
    # - task: DotNetCoreCLI@2
    #   displayName: 'Restore'
    #   inputs:
    #     command: 'restore'
    #     projects: 'api/OrderTracking.API/OrderTracking.sln'
    #     feedsToUse: 'config'
    #     nugetConfigPath: 'api/OrderTracking.API/NuGet.Config'
    #     arguments: '--no-cache --verbosity detailed'
 
    # - task: DotNetCoreCLI@2
    #   displayName: 'Build'
    #   inputs:
    #     command: 'build'
    #     projects: 'api/OrderTracking.API/OrderTracking.sln'
    #     arguments: '--configuration $(buildConfiguration) --no-restore'
 
    # - task: DotNetCoreCLI@2
    #   displayName: 'Publish'
    #   inputs:
    #     command: 'publish'
    #     publishWebProjects: false
    #     projects: 'api/OrderTracking.API/OrderTracking.API/OrderTracking.API.csproj'
    #     arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/api/OrderTracking.API'
    #     zipAfterPublish: false
 
    # - task: Docker@2
    #   displayName: 'Build & Push Docker Image'
    #   inputs:
    #     command: 'buildAndPush'
    #     containerRegistry: 'test_image'  # Azure ACR service connection
    #     repository: '$(imageName)'
    #     dockerfile: 'api/Dockerfiles/Dockerfile-cpa-backend'
    #     buildContext: '.'
    #     tags: |
    #       $(Build.BuildId)
    #       latest
    #     arguments: |
    #       --build-arg FEED_ACCESSTOKEN=your_token_here
 
# ----------------- DEPLOY TO DEV STAGE -----------------
# - stage: Deploy_Dev
#   displayName: 'Deploy to Dev'
#   dependsOn: Backend
#   jobs:
#   - job: DeployDev
#     displayName: 'Deploy to Dev Container App'
#     steps:
#     - task: AzureCLI@2
#       inputs:
#           azureSubscription: 'TEAM-CLAND-01 (8de261da-54e3-4334-9076-284334fc2305)'
#           scriptType: 'bash'
#           scriptLocation: 'inlineScript'
#           inlineScript: 'az containerapp update --name testapi001 --resource-group rg-digital-poc  --image containertestapp001.azurecr.io/kraken-backend:latest'
#       displayName: 'Build Backend Application'

 
# # ----------------- DEPLOY TO UAT STAGE -----------------
# - stage: Deploy_UAT
#   displayName: 'Deploy to UAT'
#   dependsOn: Deploy_Dev
#   condition: succeeded()
#   jobs:
#   - job: DeployUAT
#     displayName: 'Deploy to UAT Container App'
#     steps:
#     - task: AzureCLI@2
#       inputs:
#           azureSubscription: 'TEAM-CLAND-01 (8de261da-54e3-4334-9076-284334fc2305)'
#           scriptType: 'bash'
#           scriptLocation: 'inlineScript'
#           inlineScript: 'az containerapp update --name testapi001 --resource-group rg-digital-poc  --image containertestapp001.azurecr.io/kraken-backend:latest'
#       displayName: 'Build Backend Application'

 
# # ----------------- DEPLOY TO PROD STAGE -----------------
# - stage: Deploy_Prod
#   displayName: 'Deploy to Prod'
#   dependsOn: Deploy_UAT
#   condition: succeeded()
#   jobs:
#   - job: DeployProd
#     displayName: 'Deploy to Prod Container App'
#     steps:
#     - task: AzureCLI@2
#       inputs:
#           azureSubscription: 'TEAM-CLAND-01 (8de261da-54e3-4334-9076-284334fc2305)'
#           scriptType: 'bash'
#           scriptLocation: 'inlineScript'
#           inlineScript: 'az containerapp update --name testapi001 --resource-group rg-digital-poc  --image containertestapp001.azurecr.io/kraken-backend:latest'
#       displayName: 'Build Backend Application'
